import json
import re
from typing import Dict, List, Tuple, Optional
from django.db.models import Q
from api.models import Product, ProductVariant, Brand, Category, Color, Size
from .models import AIKnowledgeBase, UserPreference
import logging
import random

logger = logging.getLogger(__name__)


class AILanguageProcessor:
    """Xử lý ngôn ngữ tự nhiên đơn giản cho tiếng Việt"""

    # Từ đồng nghĩa cho các intent
    SEARCH_SYNONYMS = [
        'tìm', 'search', 'tìm kiếm', 'có', 'bán', 'sản phẩm', 'hàng', 'đồ',
        'áo', 'quần', 'giày', 'dép', 'túi', 'phụ kiện', 'mua', 'cần', 'muốn'
    ]

    SIZE_SYNONYMS = [
        'size', 'cỡ', 'số', 'kích thước', 'vừa', 'to', 'nhỏ', 'lớn', 'bé',
        'chọn size', 'size nào', 'đo size', 'hướng dẫn'
    ]

    ORDER_SYNONYMS = [
        'đặt hàng', 'order', 'mua', 'thanh toán', 'giỏ hàng', 'cart',
        'checkout', 'đặt', 'giao hàng', 'ship'
    ]

    GREETING_SYNONYMS = [
        'xin chào', 'hello', 'hi', 'chào', 'hey', 'good morning', 'good afternoon'
    ]

    PRICE_SYNONYMS = [
        'giá', 'price', 'bao nhiêu', 'cost', 'tiền', 'phí', 'rẻ', 'đắt', 'khuyến mãi', 'sale'
    ]

    @staticmethod
    def extract_intent(message: str) -> str:
        """Trích xuất intent từ tin nhắn"""
        message_lower = message.lower()

        # Đếm số lượng từ khóa cho mỗi intent
        search_count = sum(1 for word in AILanguageProcessor.SEARCH_SYNONYMS if word in message_lower)
        size_count = sum(1 for word in AILanguageProcessor.SIZE_SYNONYMS if word in message_lower)
        order_count = sum(1 for word in AILanguageProcessor.ORDER_SYNONYMS if word in message_lower)
        greeting_count = sum(1 for word in AILanguageProcessor.GREETING_SYNONYMS if word in message_lower)
        price_count = sum(1 for word in AILanguageProcessor.PRICE_SYNONYMS if word in message_lower)

        # Tìm intent có điểm cao nhất
        intent_scores = {
            'product_search': search_count,
            'size_help': size_count,
            'order_help': order_count,
            'greeting': greeting_count,
            'price_inquiry': price_count
        }

        max_score = max(intent_scores.values())
        if max_score == 0:
            return 'general'

        return max(intent_scores, key=intent_scores.get)

    @staticmethod
    def extract_entities(message: str) -> Dict:
        """Trích xuất các thực thể từ tin nhắn"""
        entities = {
            'colors': [],
            'sizes': [],
            'brands': [],
            'categories': [],
            'price_range': None,
            'keywords': []
        }

        message_lower = message.lower()

        # Trích xuất màu sắc
        color_patterns = ['đỏ', 'xanh', 'vàng', 'đen', 'trắng', 'xám', 'nâu', 'hồng', 'tím', 'cam']
        entities['colors'] = [color for color in color_patterns if color in message_lower]

        # Trích xuất size
        size_pattern = r'size\s*([smlxl]+|\d+)|cỡ\s*([smlxl]+|\d+)|số\s*(\d+)'
        size_matches = re.findall(size_pattern, message_lower)
        for match in size_matches:
            size = ''.join(match).upper()
            if size:
                entities['sizes'].append(size)

        # Trích xuất khoảng giá
        entities['price_range'] = AIProductSearchService.extract_price_range(message)

        # Trích xuất từ khóa chung
        words = message_lower.split()
        entities['keywords'] = [word for word in words if len(word) > 2]

        return entities


class AIProductSearchService:
    """Service để tìm kiếm sản phẩm thông minh"""
    
    @staticmethod
    def search_products(query: str, user=None, limit: int = 10) -> List[Product]:
        """Tìm kiếm sản phẩm dựa trên query"""
        # Tách từ khóa
        keywords = query.lower().split()
        
        # Tạo Q object để search
        search_q = Q()
        
        for keyword in keywords:
            search_q |= (
                Q(name__icontains=keyword) |
                Q(description__icontains=keyword) |
                Q(brand__name__icontains=keyword) |
                Q(category__title__icontains=keyword)
            )
        
        products = Product.objects.filter(search_q).distinct()
        
        # Nếu có user preferences, ưu tiên theo sở thích
        if user and hasattr(user, 'ai_preferences'):
            prefs = user.ai_preferences
            if prefs.preferred_brands:
                products = products.filter(brand__name__in=prefs.preferred_brands) | products
            if prefs.preferred_categories:
                products = products.filter(category__title__in=prefs.preferred_categories) | products
        
        return products[:limit]
    
    @staticmethod
    def extract_price_range(query: str) -> Optional[Tuple[int, int]]:
        """Trích xuất khoảng giá từ query"""
        # Tìm pattern như "dưới 500k", "từ 100k đến 300k", "khoảng 200k"
        patterns = [
            r'dưới\s+(\d+)k?',
            r'từ\s+(\d+)k?\s+đến\s+(\d+)k?',
            r'khoảng\s+(\d+)k?',
            r'(\d+)k?\s*-\s*(\d+)k?'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, query.lower())
            if match:
                groups = match.groups()
                if len(groups) == 1:
                    price = int(groups[0]) * 1000
                    if 'dưới' in pattern:
                        return (0, price)
                    else:
                        return (price - 50000, price + 50000)
                elif len(groups) == 2:
                    min_price = int(groups[0]) * 1000
                    max_price = int(groups[1]) * 1000
                    return (min_price, max_price)
        
        return None
    
    @staticmethod
    def extract_size_info(query: str) -> Optional[str]:
        """Trích xuất thông tin size từ query"""
        size_patterns = [
            r'size\s+([smlxl]+|\d+)',
            r'cỡ\s+([smlxl]+|\d+)',
            r'số\s+(\d+)'
        ]
        
        for pattern in size_patterns:
            match = re.search(pattern, query.lower())
            if match:
                return match.group(1).upper()
        
        return None


class AISizeRecommendationService:
    """Service để gợi ý size phù hợp"""
    
    @staticmethod
    def recommend_size(product: Product, user=None, user_info: Dict = None) -> Dict:
        """Gợi ý size cho sản phẩm"""
        recommendations = {
            'recommended_sizes': [],
            'explanation': '',
            'size_guide': {}
        }
        
        # Lấy thông tin size preferences của user
        if user and hasattr(user, 'ai_preferences'):
            size_prefs = user.ai_preferences.size_preferences
            category_key = product.category.title.lower()
            
            if category_key in size_prefs:
                recommended_size = size_prefs[category_key]
                recommendations['recommended_sizes'].append(recommended_size)
                recommendations['explanation'] = f"Dựa trên lịch sử mua hàng, bạn thường chọn size {recommended_size} cho {product.category.title}"
        
        # Lấy các size có sẵn cho sản phẩm
        if product.has_variants:
            available_sizes = ProductVariant.objects.filter(
                product=product, 
                stock_quantity__gt=0
            ).values_list('size__name', flat=True).distinct()
            
            recommendations['available_sizes'] = list(available_sizes)
        
        # Thêm size guide chung
        recommendations['size_guide'] = AISizeRecommendationService._get_size_guide(product.category.title)
        
        return recommendations
    
    @staticmethod
    def _get_size_guide(category: str) -> Dict:
        """Lấy hướng dẫn chọn size theo category"""
        size_guides = {
            'Áo': {
                'S': 'Ngực: 84-88cm, Eo: 74-78cm',
                'M': 'Ngực: 88-92cm, Eo: 78-82cm',
                'L': 'Ngực: 92-96cm, Eo: 82-86cm',
                'XL': 'Ngực: 96-100cm, Eo: 86-90cm'
            },
            'Quần': {
                'S': 'Eo: 68-72cm, Mông: 88-92cm',
                'M': 'Eo: 72-76cm, Mông: 92-96cm',
                'L': 'Eo: 76-80cm, Mông: 96-100cm',
                'XL': 'Eo: 80-84cm, Mông: 100-104cm'
            },
            'Giày': {
                '39': 'Dài chân: 24.5cm',
                '40': 'Dài chân: 25cm',
                '41': 'Dài chân: 25.5cm',
                '42': 'Dài chân: 26cm',
                '43': 'Dài chân: 26.5cm'
            }
        }
        
        return size_guides.get(category, {})


class AIResponseGenerator:
    """Service để tạo phản hồi AI"""
    
    @staticmethod
    def generate_response(user_message: str, user=None, context: Dict = None) -> Dict:
        """Tạo phản hồi AI cho tin nhắn của user"""
        response = {
            'message': '',
            'actions_taken': [],
            'suggested_products': [],
            'quick_replies': [],
            'metadata': {}
        }

        # Phân tích intent và entities
        intent = AILanguageProcessor.extract_intent(user_message)
        entities = AILanguageProcessor.extract_entities(user_message)

        response['metadata']['intent'] = intent
        response['metadata']['entities'] = entities

        if intent == 'product_search':
            return AIResponseGenerator._handle_product_search(user_message, user, response, entities)
        elif intent == 'size_help':
            return AIResponseGenerator._handle_size_help(user_message, user, response, entities)
        elif intent == 'order_help':
            return AIResponseGenerator._handle_order_help(user_message, user, response, entities)
        elif intent == 'greeting':
            return AIResponseGenerator._handle_greeting(user_message, user, response)
        elif intent == 'price_inquiry':
            return AIResponseGenerator._handle_price_inquiry(user_message, user, response, entities)
        else:
            return AIResponseGenerator._handle_general(user_message, user, response)
    
    @staticmethod
    def _handle_product_search(message: str, user, response: Dict, entities: Dict) -> Dict:
        """Xử lý tìm kiếm sản phẩm với entities"""
        products = AIProductSearchService.search_products(message, user)

        # Lọc theo entities nếu có
        if entities['colors']:
            color_filter = Q()
            for color in entities['colors']:
                color_filter |= Q(productvariant__color__name__icontains=color)
            products = products.filter(color_filter).distinct()

        if entities['price_range']:
            min_price, max_price = entities['price_range']
            products = products.filter(price__gte=min_price, price__lte=max_price)

        if products:
            response['suggested_products'] = products[:10]

            # Tạo message thông minh hơn
            if entities['colors']:
                color_text = ', '.join(entities['colors'])
                response['message'] = f"Tôi tìm thấy {len(products)} sản phẩm màu {color_text} phù hợp:"
            elif entities['price_range']:
                min_price, max_price = entities['price_range']
                response['message'] = f"Tôi tìm thấy {len(products)} sản phẩm trong khoảng giá {min_price:,} - {max_price:,} VND:"
            else:
                response['message'] = f"Tôi tìm thấy {len(products)} sản phẩm phù hợp với yêu cầu của bạn:"

            response['quick_replies'] = ['Xem chi tiết', 'Lọc theo giá', 'Hỗ trợ chọn size', 'Thêm vào giỏ hàng']
            response['actions_taken'].append({
                'type': 'product_search',
                'query': message,
                'results_count': len(products),
                'filters_applied': entities
            })
        else:
            response['message'] = "Xin lỗi, tôi không tìm thấy sản phẩm nào phù hợp. Bạn có thể thử:"
            response['quick_replies'] = ['Tìm theo danh mục', 'Xem sản phẩm hot', 'Thay đổi bộ lọc', 'Liên hệ hỗ trợ']

        return response
    
    @staticmethod
    def _handle_size_help(message: str, user, response: Dict, entities: Dict) -> Dict:
        """Xử lý hỗ trợ chọn size với entities"""
        if entities['sizes']:
            size = entities['sizes'][0]
            response['message'] = f"Bạn đang quan tâm đến size {size}. Tôi có thể giúp bạn kiểm tra size này có phù hợp không?"
        else:
            response['message'] = "Tôi có thể giúp bạn chọn size phù hợp! Bạn đang quan tâm đến loại sản phẩm nào?"

        response['quick_replies'] = ['Áo', 'Quần', 'Giày', 'Hướng dẫn đo size', 'Bảng size chi tiết']
        response['actions_taken'].append({
            'type': 'size_help',
            'query': message,
            'detected_sizes': entities['sizes']
        })
        return response
    
    @staticmethod
    def _handle_order_help(message: str, user, response: Dict, entities: Dict) -> Dict:
        """Xử lý hỗ trợ đặt hàng với entities"""
        response['message'] = "Tôi có thể hỗ trợ bạn đặt hàng! Bạn cần hỗ trợ gì?"
        response['quick_replies'] = ['Kiểm tra giỏ hàng', 'Hướng dẫn thanh toán', 'Theo dõi đơn hàng', 'Chính sách giao hàng']
        response['actions_taken'].append({
            'type': 'order_help',
            'query': message
        })
        return response
    
    @staticmethod
    def _handle_price_inquiry(message: str, user, response: Dict, entities: Dict) -> Dict:
        """Xử lý câu hỏi về giá"""
        if entities['price_range']:
            min_price, max_price = entities['price_range']
            products = Product.objects.filter(price__gte=min_price, price__lte=max_price)[:10]
            if products:
                response['suggested_products'] = products
                response['message'] = f"Đây là các sản phẩm trong khoảng giá {min_price:,} - {max_price:,} VND:"
            else:
                response['message'] = f"Hiện tại không có sản phẩm nào trong khoảng giá {min_price:,} - {max_price:,} VND."
        else:
            response['message'] = "Bạn muốn xem sản phẩm trong khoảng giá nào? Tôi có thể gợi ý cho bạn:"

        response['quick_replies'] = ['Dưới 200k', '200k - 500k', '500k - 1tr', 'Trên 1tr', 'Xem khuyến mãi']
        response['actions_taken'].append({
            'type': 'price_inquiry',
            'query': message,
            'price_range': entities['price_range']
        })
        return response
    
    @staticmethod
    def _handle_greeting(message: str, user, response: Dict) -> Dict:
        """Xử lý lời chào"""
        user_name = user.first_name if user and user.first_name else "bạn"
        response['message'] = f"Xin chào {user_name}! Tôi là trợ lý AI của shop. Tôi có thể giúp bạn tìm sản phẩm, chọn size, và hỗ trợ đặt hàng. Bạn cần hỗ trợ gì?"
        response['quick_replies'] = ['Tìm sản phẩm', 'Hỗ trợ chọn size', 'Kiểm tra đơn hàng', 'Xem khuyến mãi']
        return response
    
    @staticmethod
    def _handle_general(message: str, user, response: Dict) -> Dict:
        """Xử lý câu hỏi chung"""
        # Tìm trong knowledge base
        knowledge = AIKnowledgeBase.objects.filter(
            Q(question__icontains=message) | Q(keywords__contains=message.lower()),
            is_active=True
        ).first()
        
        if knowledge:
            response['message'] = knowledge.answer
        else:
            response['message'] = "Tôi chưa hiểu rõ câu hỏi của bạn. Bạn có thể hỏi tôi về sản phẩm, size, đặt hàng, hoặc chính sách của shop."
        
        response['quick_replies'] = ['Tìm sản phẩm', 'Hỗ trợ chọn size', 'Chính sách đổi trả', 'Liên hệ hỗ trợ']
        return response
