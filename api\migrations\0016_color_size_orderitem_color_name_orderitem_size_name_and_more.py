# Generated by Django 5.2.3 on 2025-07-12 07:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0015_merge_20250712_0251'),
    ]

    operations = [
        migrations.CreateModel(
            name='Color',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('hex_code', models.CharField(help_text='Mã màu hex (ví dụ: #FF0000)', max_length=7)),
            ],
            options={
                'verbose_name': '<PERSON><PERSON>u sắc',
                'verbose_name_plural': '<PERSON>àu sắc',
            },
        ),
        migrations.CreateModel(
            name='Size',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=10, unique=True)),
                ('order', models.IntegerField(default=0, help_text='Thứ tự hiển thị')),
            ],
            options={
                'verbose_name': 'Kích cỡ',
                'verbose_name_plural': 'Kích cỡ',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.AddField(
            model_name='orderitem',
            name='color_name',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='orderitem',
            name='size_name',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='has_variants',
            field=models.BooleanField(default=False, help_text='Sản phẩm có biến thể màu sắc/size'),
        ),
        migrations.CreateModel(
            name='ProductVariant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price', models.DecimalField(decimal_places=0, help_text='Giá cho biến thể này', max_digits=12)),
                ('stock_quantity', models.IntegerField(default=0, help_text='Số lượng tồn kho')),
                ('sku', models.CharField(blank=True, help_text='Mã SKU riêng cho biến thể', max_length=100, unique=True)),
                ('image', models.ImageField(blank=True, help_text='Hình ảnh riêng cho biến thể (tùy chọn)', null=True, upload_to='')),
                ('color', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.color')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variants', to='api.product')),
                ('size', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.size')),
            ],
            options={
                'verbose_name': 'Biến thể sản phẩm',
                'verbose_name_plural': 'Biến thể sản phẩm',
                'unique_together': {('product', 'color', 'size')},
            },
        ),
        migrations.AddField(
            model_name='orderitem',
            name='product_variant',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='api.productvariant'),
        ),
    ]
