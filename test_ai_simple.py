#!/usr/bin/env python3
"""
Simple test for AI Chat endpoint
"""

import requests
import json

def test_ai_chat_simple():
    print("🧪 Testing AI Chat endpoint...")
    
    # Test data without session_id
    test_data = {
        "message": "xin chào",
        "context": {}
    }
    
    print(f"Request data: {json.dumps(test_data, indent=2)}")
    
    try:
        # First, try without authentication to see the error
        print("\n1. Testing without authentication...")
        response = requests.post(
            'http://localhost:8000/ai/chat/',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        # Test with session_id
        print("\n2. Testing with empty session_id...")
        test_data_with_session = {
            "message": "xin chào",
            "session_id": "",
            "context": {}
        }
        
        response = requests.post(
            'http://localhost:8000/ai/chat/',
            json=test_data_with_session,
            headers={'Content-Type': 'application/json'}
        )
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        # Test with null session_id
        print("\n3. Testing with null session_id...")
        test_data_with_null = {
            "message": "xin chào",
            "session_id": None,
            "context": {}
        }
        
        response = requests.post(
            'http://localhost:8000/ai/chat/',
            json=test_data_with_null,
            headers={'Content-Type': 'application/json'}
        )
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_ai_chat_simple()
