# Generated by Django 5.2.3 on 2025-06-28 09:13

from django.db import migrations
from decimal import Decimal


def convert_inr_to_vnd(apps, schema_editor):
    """
    Convert prices from INR to VND
    Approximate conversion rate: 1 INR = 300 VND
    """
    Product = apps.get_model('api', 'Product')
    Order = apps.get_model('api', 'Order')
    OrderItem = apps.get_model('api', 'OrderItem')

    # Convert rate: 1 INR ≈ 300 VND (approximate)
    conversion_rate = Decimal('300')

    # Convert Product prices
    for product in Product.objects.all():
        if product.price:
            product.price = int(product.price * conversion_rate)
            product.save()

    # Convert Order prices
    for order in Order.objects.all():
        if order.taxPrice:
            order.taxPrice = int(order.taxPrice * conversion_rate)
        if order.shippingPrice:
            order.shippingPrice = int(order.shippingPrice * conversion_rate)
        if order.totalPrice:
            order.totalPrice = int(order.totalPrice * conversion_rate)
        order.save()

    # Convert OrderItem prices
    for item in OrderItem.objects.all():
        if item.price:
            item.price = int(item.price * conversion_rate)
            item.save()


def reverse_convert_vnd_to_inr(apps, schema_editor):
    """
    Reverse conversion from VND to INR
    """
    Product = apps.get_model('api', 'Product')
    Order = apps.get_model('api', 'Order')
    OrderItem = apps.get_model('api', 'OrderItem')

    # Reverse conversion rate
    conversion_rate = Decimal('300')

    # Convert Product prices back
    for product in Product.objects.all():
        if product.price:
            product.price = product.price / conversion_rate
            product.save()

    # Convert Order prices back
    for order in Order.objects.all():
        if order.taxPrice:
            order.taxPrice = order.taxPrice / conversion_rate
        if order.shippingPrice:
            order.shippingPrice = order.shippingPrice / conversion_rate
        if order.totalPrice:
            order.totalPrice = order.totalPrice / conversion_rate
        order.save()

    # Convert OrderItem prices back
    for item in OrderItem.objects.all():
        if item.price:
            item.price = item.price / conversion_rate
            item.save()


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0005_alter_order_shippingprice_alter_order_taxprice_and_more'),
    ]

    operations = [
        migrations.RunPython(convert_inr_to_vnd, reverse_convert_vnd_to_inr),
    ]
