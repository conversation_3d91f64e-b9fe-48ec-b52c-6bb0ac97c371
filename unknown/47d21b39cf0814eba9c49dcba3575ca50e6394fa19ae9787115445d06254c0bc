# Generated by Django 5.2.3 on 2025-07-03 03:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0007_payboxwallet_payboxtransaction'),
    ]

    operations = [
        migrations.CreateModel(
            name='Coupon',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=30, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('discount_amount', models.DecimalField(decimal_places=0, help_text='Số tiền giảm (VND)', max_digits=12)),
                ('min_order_amount', models.DecimalField(decimal_places=0, help_text='Đơn tối thiểu để áp dụng (VND)', max_digits=12)),
                ('valid_from', models.DateTimeField()),
                ('valid_to', models.DateTime<PERSON>ield()),
                ('is_active', models.<PERSON><PERSON><PERSON><PERSON>ield(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
