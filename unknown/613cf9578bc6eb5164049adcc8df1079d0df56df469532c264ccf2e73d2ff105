# Generated by Django 5.2.3 on 2025-06-29 01:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0006_auto_20250628_1613'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PayboxWallet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('balance', models.DecimalField(decimal_places=0, default=0, help_text='Số dư ví tính bằng VND', max_digits=12)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='paybox_wallet', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Paybox Wallet',
                'verbose_name_plural': 'Paybox Wallets',
            },
        ),
        migrations.CreateModel(
            name='PayboxTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('DEPOSIT', 'Nạp tiền'), ('PAYMENT', 'Thanh toán đơn hàng'), ('REFUND', 'Hoàn tiền'), ('TRANSFER', 'Chuyển tiền')], max_length=20)),
                ('amount', models.DecimalField(decimal_places=0, help_text='Số tiền giao dịch tính bằng VND', max_digits=12)),
                ('status', models.CharField(choices=[('PENDING', 'Đang xử lý'), ('COMPLETED', 'Hoàn thành'), ('FAILED', 'Thất bại'), ('CANCELLED', 'Đã hủy')], default='PENDING', max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('stripe_payment_intent_id', models.CharField(blank=True, max_length=255, null=True)),
                ('balance_before', models.DecimalField(decimal_places=0, help_text='Số dư trước giao dịch', max_digits=12)),
                ('balance_after', models.DecimalField(decimal_places=0, help_text='Số dư sau giao dịch', max_digits=12)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='paybox_transactions', to='api.order')),
                ('wallet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='api.payboxwallet')),
            ],
            options={
                'verbose_name': 'Paybox Transaction',
                'verbose_name_plural': 'Paybox Transactions',
                'ordering': ['-created_at'],
            },
        ),
    ]
