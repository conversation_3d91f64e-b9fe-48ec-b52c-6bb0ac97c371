.login-page {
  background-color: #f8f9fa;
  padding: 80px 0;
  min-height: calc(100vh - 150px);
  background-image: url('https://images.unsplash.com/photo-1445205170230-053b83016050?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80');
  background-size: cover;
  background-position: center;
  position: relative;
}

.login-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.login-container {
  max-width: 550px;
  margin: 0 auto;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 0;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(to bottom, #000, #333);
}

.breadcrumb-nav {
  padding: 20px 30px;
  font-size: 14px;
  color: #6c757d;
  border-bottom: 1px solid #f1f1f1;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.breadcrumb-nav a {
  color: #000;
  text-decoration: none;
  transition: color 0.2s;
  font-weight: 500;
}

.breadcrumb-nav a:hover {
  color: #666;
}

.auth-tabs {
  display: flex;
  border-bottom: 1px solid #f1f1f1;
}

.auth-tab {
  flex: 1;
  text-align: center;
  padding: 20px 15px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.3s;
  position: relative;
}

.auth-tab.active {
  color: #000;
}

.auth-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #000;
}

.auth-tab a {
  color: inherit;
  text-decoration: none;
  display: block;
  letter-spacing: 1px;
}

.auth-content {
  padding: 40px 50px;
}

.auth-benefits {
  margin-bottom: 30px;
  color: #495057;
  font-size: 15px;
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: 20px;
  border-left: 4px solid #000;
}

.form-group {
  margin-bottom: 25px;
}

.form-label {
  font-weight: 600;
  color: #000;
  margin-bottom: 10px;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.form-control {
  height: 50px;
  border-radius: 0;
  border: 1px solid #ced4da;
  padding: 10px 15px;
  font-size: 15px;
  transition: all 0.3s;
}

.form-control:focus {
  border-color: #000;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.forgot-password {
  text-align: right;
  margin-bottom: 25px;
}

.forgot-password a {
  color: #000;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s;
  font-weight: 500;
}

.forgot-password a:hover {
  text-decoration: underline;
}

.auth-info {
  font-size: 14px;
  color: #6c757d;
  line-height: 1.6;
  text-align: center;
}

.auth-info a {
  color: #000;
  text-decoration: none;
  transition: color 0.2s;
  font-weight: 500;
}

.auth-info a:hover {
  text-decoration: underline;
}

.auth-button {
  width: 100%;
  background-color: #000;
  border: none;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 0;
  transition: all 0.3s;
  margin-bottom: 25px;
  letter-spacing: 2px;
  color: #fff;
}

.auth-button:hover {
  background-color: #333;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.auth-button:active {
  transform: translateY(0);
}

.auth-separator {
  position: relative;
  text-align: center;
  margin: 25px 0;
}

.auth-separator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e9ecef;
}

.auth-separator span {
  position: relative;
  background-color: #fff;
  padding: 0 15px;
  color: #6c757d;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 25px;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  border-radius: 0;
  font-weight: 500;
  transition: all 0.3s;
  letter-spacing: 1px;
  border: 1px solid;
  background-color: transparent;
}

.social-btn i {
  margin-right: 10px;
  font-size: 18px;
}

.social-btn.facebook {
  border-color: #3b5998;
  color: #3b5998;
}

.social-btn.facebook:hover {
  background-color: #3b5998;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(59, 89, 152, 0.3);
}

.social-btn.google {
  border-color: #db4437;
  color: #db4437;
}

.social-btn.google:hover {
  background-color: #db4437;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(219, 68, 55, 0.3);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .login-container {
    background-color: rgba(30, 30, 30, 0.95);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
  }
  
  .login-container::before {
    background: linear-gradient(to bottom, #fff, #999);
  }
  
  .breadcrumb-nav {
    color: #adb5bd;
    border-bottom-color: #333;
  }
  
  .breadcrumb-nav a {
    color: #fff;
  }
  
  .breadcrumb-nav a:hover {
    color: #ccc;
  }
  
  .auth-tabs {
    border-bottom-color: #333;
  }
  
  .auth-tab {
    color: #adb5bd;
  }
  
  .auth-tab.active {
    color: #fff;
  }
  
  .auth-tab.active::after {
    background-color: #fff;
  }
  
  .auth-benefits {
    color: #adb5bd;
    background-color: #2c2c2c;
    border-left-color: #fff;
  }
  
  .form-label {
    color: #fff;
  }
  
  .form-control {
    background-color: #2c2c2c;
    border-color: #444;
    color: #fff;
  }
  
  .form-control:focus {
    border-color: #fff;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
  }
  
  .forgot-password a {
    color: #fff;
  }
  
  .auth-button {
    background-color: #fff;
    color: #000;
  }
  
  .auth-button:hover {
    background-color: #ccc;
  }
  
  .auth-separator::before {
    background-color: #444;
  }
  
  .auth-separator span {
    background-color: #1e1e1e;
    color: #adb5bd;
  }
  
  .social-btn.facebook {
    border-color: #3b5998;
    color: #3b5998;
  }
  
  .social-btn.google {
    border-color: #db4437;
    color: #db4437;
  }
  
  .auth-info {
    color: #adb5bd;
  }
  
  .auth-info a {
    color: #fff;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .login-page {
    padding: 40px 20px;
  }
  
  .login-container {
    border-radius: 0;
  }
  
  .auth-content {
    padding: 30px 25px;
  }
  
  .auth-tab {
    padding: 15px 10px;
    font-size: 14px;
  }
  
  .form-control {
    height: 45px;
  }
  
  .auth-button {
    height: 45px;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-container {
  animation: fadeIn 0.6s ease-out;
}
