/* Profile Page Styles */
.profile-page {
  padding: 20px 0;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Profile Sidebar */
.profile-sidebar {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 30px 20px;
  height: fit-content;
  position: sticky;
  top: 20px;
}

.profile-avatar-section {
  text-align: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #007bff;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.profile-avatar:hover {
  transform: scale(1.05);
  border-color: #0056b3;
}

.avatar-upload-btn {
  position: relative;
  display: inline-block;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.avatar-upload-btn:hover {
  opacity: 0.8;
}

.avatar-upload-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  pointer-events: none;
}

.profile-user-name {
  font-size: 1.4rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.profile-user-email {
  color: #666;
  font-size: 0.95rem;
  margin-bottom: 0;
}

.profile-nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.profile-nav-item {
  margin-bottom: 8px;
}

.profile-nav-link {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  color: #555;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.profile-nav-link:hover {
  background-color: #f8f9fa;
  color: #007bff;
  text-decoration: none;
}

.profile-nav-link.active {
  background-color: #007bff;
  color: white;
}

.profile-nav-link i {
  margin-right: 10px;
  width: 18px;
  text-align: center;
}

.logout-link {
  color: #dc3545 !important;
  border-top: 1px solid #e9ecef;
  margin-top: 15px;
  padding-top: 15px;
}

.logout-link:hover {
  background-color: #f8d7da !important;
  color: #721c24 !important;
}

/* Profile Main Content */
.profile-main-content {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.profile-content-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 25px 30px;
  margin-bottom: 0;
}

.profile-content-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.profile-content-subtitle {
  font-size: 0.95rem;
  opacity: 0.9;
  margin: 5px 0 0 0;
}

/* Profile Tabs */
.profile-tabs {
  border-bottom: 1px solid #e9ecef;
}

.profile-tabs .nav-link {
  color: #666;
  font-weight: 500;
  padding: 15px 20px;
  border: none;
  border-bottom: 3px solid transparent;
}

.profile-tabs .nav-link:hover {
  color: #007bff;
  background-color: #f8f9fa;
}

.profile-tabs .nav-link.active {
  color: #007bff;
  background-color: white;
  border-bottom-color: #007bff;
}

.profile-tab-content {
  padding: 30px;
}

/* Personal Info Form */
.personal-info-form {
  max-width: 600px;
}

.form-section {
  margin-bottom: 30px;
}

.form-section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f8f9fa;
}

.form-row {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  font-weight: 500;
  color: #555;
  margin-bottom: 8px;
}

.form-control {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 0.95rem;
}

.btn-update-profile {
  background-color: #007bff;
  border-color: #007bff;
  padding: 12px 30px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-update-profile:hover {
  background-color: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
}

/* Order History */
.order-item {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.order-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f8f9fa;
}

.order-id {
  font-weight: 600;
  color: #333;
}

.order-date {
  color: #666;
  font-size: 0.9rem;
}

.order-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-processing {
  background-color: #cce5ff;
  color: #004085;
}

.status-shipped {
  background-color: #d4edda;
  color: #155724;
}

.status-delivered {
  background-color: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.order-total {
  font-size: 1.1rem;
  font-weight: 600;
  color: #e41e3f;
}

/* Favorite Products */
.favorite-product-card {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.favorite-product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.favorite-product-card .card-img-top {
  transition: transform 0.3s ease;
}

.favorite-product-card:hover .card-img-top {
  transform: scale(1.05);
}

.favorite-product-card .card-title {
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Orders Wrapper */
.orders-wrapper {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.orders-wrapper .table {
  margin-bottom: 0;
}

.orders-wrapper .table th {
  background-color: #f8f9fa;
  border-top: none;
  font-weight: 600;
  color: #555;
  padding: 15px 12px;
}

.orders-wrapper .table td {
  padding: 15px 12px;
  vertical-align: middle;
  border-top: 1px solid #e9ecef;
}

.orders-wrapper .table tbody tr:hover {
  background-color: #f8f9fa;
}

.orders-wrapper .btn {
  padding: 6px 12px;
  font-size: 0.875rem;
}

/* Success Message */
.success-message {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.success-message i {
  color: #28a745;
  font-size: 1.2rem;
}

/* Error Message */
.error-message {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.error-message i {
  color: #dc3545;
  font-size: 1.2rem;
}

/* Profile View Mode */
.profile-view-mode {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
}

.profile-info-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.info-item {
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;
  margin-bottom: 5px;
  display: block;
}

.info-value {
  font-size: 1rem;
  color: #333;
  font-weight: 500;
  min-height: 24px;
}

.info-value:empty::after {
  content: "Chưa cập nhật";
  color: #999;
  font-style: italic;
}

/* Alert styling */
.alert {
  border-radius: 8px;
  border: none;
  padding: 15px;
}

.alert-info {
  background-color: #e3f2fd;
  color: #0277bd;
  border-left: 4px solid #2196f3;
}

.alert-info i {
  color: #2196f3;
  margin-right: 8px;
}

/* Profile Stats */
.profile-stats {
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
  border-top: 1px solid #e9ecef;
  margin-top: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #007bff;
  display: block;
}

.stat-label {
  font-size: 0.85rem;
  color: #666;
  margin-top: 5px;
}

/* Upload Progress */
.upload-progress {
  margin-top: 10px;
}

.progress {
  height: 4px;
  background-color: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #007bff;
  transition: width 0.3s ease;
}

/* Form Validation */
.form-control.is-invalid {
  border-color: #dc3545;
}

.form-control.is-valid {
  border-color: #28a745;
}

.invalid-feedback {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 5px;
}

.valid-feedback {
  color: #28a745;
  font-size: 0.875rem;
  margin-top: 5px;
}

/* Loading States */
.btn-loading {
  position: relative;
  pointer-events: none;
}

.btn-loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  margin: auto;
  border: 2px solid transparent;
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .profile-sidebar {
    margin-bottom: 20px;
    position: static;
  }

  .profile-avatar {
    width: 100px;
    height: 100px;
  }

  .profile-content-header {
    padding: 20px;
  }

  .profile-tab-content {
    padding: 20px;
  }

  .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .profile-stats {
    flex-direction: column;
    gap: 15px;
  }

  .form-row {
    margin-bottom: 15px;
  }

  .info-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .profile-view-mode {
    padding: 20px;
  }

  .info-item {
    padding: 12px;
  }
}
