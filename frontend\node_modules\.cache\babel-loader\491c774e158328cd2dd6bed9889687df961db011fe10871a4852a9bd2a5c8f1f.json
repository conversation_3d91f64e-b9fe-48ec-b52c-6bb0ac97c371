{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Container } from \"react-bootstrap\";\nimport { Route, Routes, useLocation } from \"react-router-dom\";\nimport Header from \"./components/header\";\nimport Footer from \"./components/footer\";\nimport { ToastContainer } from \"react-toastify\";\n\n// User pages\nimport HomePage from \"./pages/homePage\";\nimport ProductPage from \"./pages/productPage\";\nimport CartPage from \"./pages/cartPage\";\nimport LoginPage from \"./pages/loginPage\";\nimport RegisterPage from \"./pages/registerPage\";\nimport ProfilePage from \"./pages/profilePage\";\nimport Logout from \"./pages/logout\";\nimport ShippingPage from \"./pages/shippingPage\";\nimport PlacerOrderPage from \"./pages/placeOrderPage\";\nimport OrderDetailsPage from \"./pages/orderDetailsPage\";\nimport ConfirmationPage from \"./pages/confirmationPage\";\nimport PaymentPage from \"./pages/paymentPage\";\nimport SearchPage from \"./pages/searchPage\";\nimport PayboxPage from \"./pages/PayboxPage\";\nimport FavoritesPage from \"./pages/favoritesPage\";\n\n// Admin pages\nimport AdminDashboard from \"./pages/admin/AdminDashboard\";\nimport AdminProducts from \"./pages/admin/AdminProducts\";\nimport AdminOrders from \"./pages/admin/AdminOrders\";\nimport AdminCategories from \"./pages/admin/AdminCategories\";\nimport AdminUsers from \"./pages/admin/AdminUsers\";\nimport AdminBrands from \"./pages/admin/AdminBrands\";\nimport AdminReviews from \"./pages/admin/AdminReviews\";\nimport AdminPaybox from \"./pages/admin/AdminPaybox\";\nimport AdminRefund from \"./pages/admin/AdminRefund\";\nimport AdminCoupons from \"./pages/admin/AdminCoupons\";\nimport AdminChat from \"./pages/admin/AdminChat\";\nimport AdminAIChat from \"./pages/admin/AdminAIChat\";\n\n// Components\nimport FloatingChatButton from \"./components/FloatingChatButton\";\n\n// Context providers\nimport { ProductsProvider } from \"./context/productsContext\";\nimport { CartProvider } from \"./context/cartContext\";\nimport { UserProvider } from \"./context/userContext\";\nimport { PayboxProvider } from \"./context/payboxContext\";\nimport { FavoriteProvider } from \"./context/favoriteContext\";\n\n// Other components\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport UserChat from \"./pages/UserChat\";\nimport \"./App.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContent = () => {\n  _s();\n  const location = useLocation();\n  const [keyword, setKeyword] = useState(\"\");\n  const queryParams = new URLSearchParams(window.location.search);\n  const keywordParam = queryParams.get(\"keyword\") || \"\";\n  useEffect(() => {\n    setKeyword(keywordParam);\n  }, [keywordParam]);\n  const isAdminRoute = location.pathname.startsWith(\"/admin\");\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 3000\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 8\n    }, this), /*#__PURE__*/_jsxDEV(UserProvider, {\n      children: /*#__PURE__*/_jsxDEV(PayboxProvider, {\n        children: [!isAdminRoute && /*#__PURE__*/_jsxDEV(Header, {\n          keyword: keyword,\n          setKeyword: setKeyword\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: isAdminRoute ? \"\" : \"py-3\",\n          children: /*#__PURE__*/_jsxDEV(ProductsProvider, {\n            children: /*#__PURE__*/_jsxDEV(CartProvider, {\n              children: /*#__PURE__*/_jsxDEV(FavoriteProvider, {\n                children: !isAdminRoute ? /*#__PURE__*/_jsxDEV(Container, {\n                  children: /*#__PURE__*/_jsxDEV(Routes, {\n                    children: [/*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/\",\n                      element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 80,\n                        columnNumber: 50\n                      }, this),\n                      exact: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 80,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/search\",\n                      element: /*#__PURE__*/_jsxDEV(SearchPage, {\n                        keyword: keyword\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 81,\n                        columnNumber: 56\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 81,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/login\",\n                      element: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 82,\n                        columnNumber: 55\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 82,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/logout\",\n                      element: /*#__PURE__*/_jsxDEV(Logout, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 83,\n                        columnNumber: 56\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 83,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/register\",\n                      element: /*#__PURE__*/_jsxDEV(RegisterPage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 84,\n                        columnNumber: 58\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 84,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/profile\",\n                      element: /*#__PURE__*/_jsxDEV(ProfilePage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 85,\n                        columnNumber: 57\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 85,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/paybox\",\n                      element: /*#__PURE__*/_jsxDEV(PayboxPage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 86,\n                        columnNumber: 56\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 86,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/products/:id\",\n                      element: /*#__PURE__*/_jsxDEV(ProductPage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 87,\n                        columnNumber: 62\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 87,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/orders/:id\",\n                      element: /*#__PURE__*/_jsxDEV(OrderDetailsPage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 88,\n                        columnNumber: 60\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 88,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/payment\",\n                      element: /*#__PURE__*/_jsxDEV(PaymentPage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 89,\n                        columnNumber: 57\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 89,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/shipping\",\n                      element: /*#__PURE__*/_jsxDEV(ShippingPage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 90,\n                        columnNumber: 58\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 90,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/confirmation\",\n                      element: /*#__PURE__*/_jsxDEV(ConfirmationPage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 91,\n                        columnNumber: 62\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 91,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/placeorder\",\n                      element: /*#__PURE__*/_jsxDEV(PlacerOrderPage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 92,\n                        columnNumber: 60\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 92,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/cart\",\n                      element: /*#__PURE__*/_jsxDEV(CartPage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 93,\n                        columnNumber: 54\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 93,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/user/chat\",\n                      element: /*#__PURE__*/_jsxDEV(UserChat, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 94,\n                        columnNumber: 59\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 94,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Route, {\n                      path: \"/favorites\",\n                      element: /*#__PURE__*/_jsxDEV(FavoritesPage, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 95,\n                        columnNumber: 59\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 95,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 79,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Routes, {\n                  children: [/*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/admin\",\n                    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                      adminOnly: true,\n                      children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 100,\n                        columnNumber: 86\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 100,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/admin/products\",\n                    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                      adminOnly: true,\n                      children: /*#__PURE__*/_jsxDEV(AdminProducts, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 101,\n                        columnNumber: 95\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 101,\n                      columnNumber: 62\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 101,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/admin/orders\",\n                    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                      adminOnly: true,\n                      children: /*#__PURE__*/_jsxDEV(AdminOrders, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 102,\n                        columnNumber: 93\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 102,\n                      columnNumber: 60\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/admin/categories\",\n                    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                      adminOnly: true,\n                      children: /*#__PURE__*/_jsxDEV(AdminCategories, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 103,\n                        columnNumber: 97\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 103,\n                      columnNumber: 64\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/admin/users\",\n                    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                      adminOnly: true,\n                      children: /*#__PURE__*/_jsxDEV(AdminUsers, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 104,\n                        columnNumber: 92\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 104,\n                      columnNumber: 59\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/admin/brands\",\n                    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                      adminOnly: true,\n                      children: /*#__PURE__*/_jsxDEV(AdminBrands, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 105,\n                        columnNumber: 93\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 105,\n                      columnNumber: 60\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/admin/reviews\",\n                    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                      adminOnly: true,\n                      children: /*#__PURE__*/_jsxDEV(AdminReviews, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 106,\n                        columnNumber: 94\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 106,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/admin/chat\",\n                    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                      adminOnly: true,\n                      children: /*#__PURE__*/_jsxDEV(AdminChat, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 107,\n                        columnNumber: 91\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 107,\n                      columnNumber: 58\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/admin/ai-chat\",\n                    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                      adminOnly: true,\n                      children: /*#__PURE__*/_jsxDEV(AdminAIChat, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 108,\n                        columnNumber: 94\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 108,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/admin/paybox\",\n                    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                      adminOnly: true,\n                      children: /*#__PURE__*/_jsxDEV(AdminPaybox, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 109,\n                        columnNumber: 93\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 109,\n                      columnNumber: 60\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/admin/refunds\",\n                    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                      adminOnly: true,\n                      children: /*#__PURE__*/_jsxDEV(AdminRefund, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 110,\n                        columnNumber: 94\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 110,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/admin/coupons\",\n                    element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                      adminOnly: true,\n                      children: /*#__PURE__*/_jsxDEV(AdminCoupons, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 111,\n                        columnNumber: 94\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 111,\n                      columnNumber: 61\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 111,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), !isAdminRoute && /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 29\n        }, this), !isAdminRoute && /*#__PURE__*/_jsxDEV(FloatingChatButton, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(AppContent, \"xhIPILrwo1eIwp1MEGIVhlhlvbI=\", false, function () {\n  return [useLocation];\n});\n_c = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 10\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppContent\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Route", "Routes", "useLocation", "Header", "Footer", "ToastContainer", "HomePage", "ProductPage", "CartPage", "LoginPage", "RegisterPage", "ProfilePage", "Logout", "ShippingPage", "PlacerOrderPage", "OrderDetailsPage", "ConfirmationPage", "PaymentPage", "SearchPage", "PayboxPage", "FavoritesPage", "AdminDashboard", "AdminProducts", "AdminOrders", "AdminCategories", "AdminUsers", "AdminBrands", "AdminReviews", "AdminPaybox", "AdminRefund", "AdminCoupons", "AdminChat", "AdminAIChat", "FloatingChatButton", "ProductsProvider", "CartProvider", "UserProvider", "PayboxProvider", "Favorite<PERSON>rovider", "ProtectedRoute", "UserChat", "jsxDEV", "_jsxDEV", "A<PERSON><PERSON><PERSON>nt", "_s", "location", "keyword", "setKeyword", "queryParams", "URLSearchParams", "window", "search", "keywordParam", "get", "isAdminRoute", "pathname", "startsWith", "children", "position", "autoClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "path", "element", "exact", "adminOnly", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { Container } from \"react-bootstrap\";\nimport { Route, Routes, useLocation } from \"react-router-dom\";\nimport Header from \"./components/header\";\nimport Footer from \"./components/footer\";\nimport { ToastContainer } from \"react-toastify\";\n\n// User pages\nimport HomePage from \"./pages/homePage\";\nimport ProductPage from \"./pages/productPage\";\nimport CartPage from \"./pages/cartPage\";\nimport LoginPage from \"./pages/loginPage\";\nimport RegisterPage from \"./pages/registerPage\";\nimport ProfilePage from \"./pages/profilePage\";\nimport Logout from \"./pages/logout\";\nimport ShippingPage from \"./pages/shippingPage\";\nimport PlacerOrderPage from \"./pages/placeOrderPage\";\nimport OrderDetailsPage from \"./pages/orderDetailsPage\";\nimport ConfirmationPage from \"./pages/confirmationPage\";\nimport PaymentPage from \"./pages/paymentPage\";\nimport SearchPage from \"./pages/searchPage\";\nimport PayboxPage from \"./pages/PayboxPage\";\nimport FavoritesPage from \"./pages/favoritesPage\";\n\n// Admin pages\nimport AdminDashboard from \"./pages/admin/AdminDashboard\";\nimport AdminProducts from \"./pages/admin/AdminProducts\";\nimport AdminOrders from \"./pages/admin/AdminOrders\";\nimport AdminCategories from \"./pages/admin/AdminCategories\";\nimport AdminUsers from \"./pages/admin/AdminUsers\";\nimport AdminBrands from \"./pages/admin/AdminBrands\";\nimport AdminReviews from \"./pages/admin/AdminReviews\";\nimport AdminPaybox from \"./pages/admin/AdminPaybox\";\nimport AdminRefund from \"./pages/admin/AdminRefund\";\nimport AdminCoupons from \"./pages/admin/AdminCoupons\";\nimport AdminChat from \"./pages/admin/AdminChat\";\nimport AdminAIChat from \"./pages/admin/AdminAIChat\";\n\n// Components\nimport FloatingChatButton from \"./components/FloatingChatButton\";\n\n// Context providers\nimport { ProductsProvider } from \"./context/productsContext\";\nimport { CartProvider } from \"./context/cartContext\";\nimport { UserProvider } from \"./context/userContext\";\nimport { PayboxProvider } from \"./context/payboxContext\";\nimport { FavoriteProvider } from \"./context/favoriteContext\";\n\n// Other components\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport UserChat from \"./pages/UserChat\";\n\nimport \"./App.css\";\n\nconst AppContent = () => {\n  const location = useLocation();\n  const [keyword, setKeyword] = useState(\"\");\n  const queryParams = new URLSearchParams(window.location.search);\n  const keywordParam = queryParams.get(\"keyword\") || \"\";\n\n  useEffect(() => {\n    setKeyword(keywordParam);\n  }, [keywordParam]);\n\n  const isAdminRoute = location.pathname.startsWith(\"/admin\");\n\n  return (\n    <div>\n       <ToastContainer position=\"top-right\" autoClose={3000} />\n      <UserProvider>\n        <PayboxProvider>\n          {!isAdminRoute && <Header keyword={keyword} setKeyword={setKeyword} />}\n          <main className={isAdminRoute ? \"\" : \"py-3\"}>\n            <ProductsProvider>\n              <CartProvider>\n                <FavoriteProvider>\n                  {!isAdminRoute ? (\n                    <Container>\n                      <Routes>\n                        <Route path=\"/\" element={<HomePage />} exact />\n                        <Route path=\"/search\" element={<SearchPage keyword={keyword} />} />\n                        <Route path=\"/login\" element={<LoginPage />} />\n                        <Route path=\"/logout\" element={<Logout />} />\n                        <Route path=\"/register\" element={<RegisterPage />} />\n                        <Route path=\"/profile\" element={<ProfilePage />} />\n                        <Route path=\"/paybox\" element={<PayboxPage />} />\n                        <Route path=\"/products/:id\" element={<ProductPage />} />\n                        <Route path=\"/orders/:id\" element={<OrderDetailsPage />} />\n                        <Route path=\"/payment\" element={<PaymentPage />} />\n                        <Route path=\"/shipping\" element={<ShippingPage />} />\n                        <Route path=\"/confirmation\" element={<ConfirmationPage />} />\n                        <Route path=\"/placeorder\" element={<PlacerOrderPage />} />\n                        <Route path=\"/cart\" element={<CartPage />} />\n                        <Route path=\"/user/chat\" element={<UserChat />} />\n                        <Route path=\"/favorites\" element={<FavoritesPage />} />\n                      </Routes>\n                    </Container>\n                  ) : (\n                    <Routes>\n                      <Route path=\"/admin\" element={<ProtectedRoute adminOnly={true}><AdminDashboard /></ProtectedRoute>} />\n                      <Route path=\"/admin/products\" element={<ProtectedRoute adminOnly={true}><AdminProducts /></ProtectedRoute>} />\n                      <Route path=\"/admin/orders\" element={<ProtectedRoute adminOnly={true}><AdminOrders /></ProtectedRoute>} />\n                      <Route path=\"/admin/categories\" element={<ProtectedRoute adminOnly={true}><AdminCategories /></ProtectedRoute>} />\n                      <Route path=\"/admin/users\" element={<ProtectedRoute adminOnly={true}><AdminUsers /></ProtectedRoute>} />\n                      <Route path=\"/admin/brands\" element={<ProtectedRoute adminOnly={true}><AdminBrands /></ProtectedRoute>} />\n                      <Route path=\"/admin/reviews\" element={<ProtectedRoute adminOnly={true}><AdminReviews /></ProtectedRoute>} />\n                      <Route path=\"/admin/chat\" element={<ProtectedRoute adminOnly={true}><AdminChat /></ProtectedRoute>} />\n                      <Route path=\"/admin/ai-chat\" element={<ProtectedRoute adminOnly={true}><AdminAIChat /></ProtectedRoute>} />\n                      <Route path=\"/admin/paybox\" element={<ProtectedRoute adminOnly={true}><AdminPaybox /></ProtectedRoute>} />\n                      <Route path=\"/admin/refunds\" element={<ProtectedRoute adminOnly={true}><AdminRefund /></ProtectedRoute>} />\n                      <Route path=\"/admin/coupons\" element={<ProtectedRoute adminOnly={true}><AdminCoupons /></ProtectedRoute>} />\n                    </Routes>\n                  )}\n                </FavoriteProvider>\n              </CartProvider>\n            </ProductsProvider>\n          </main>\n          {!isAdminRoute && <Footer />}\n          {!isAdminRoute && <FloatingChatButton />}\n        </PayboxProvider>\n      </UserProvider>\n    </div>\n  );\n};\n\nfunction App() {\n  return <AppContent />;\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,KAAK,EAAEC,MAAM,EAAEC,WAAW,QAAQ,kBAAkB;AAC7D,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,cAAc,QAAQ,gBAAgB;;AAE/C;AACA,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,eAAe,MAAM,wBAAwB;AACpD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,aAAa,MAAM,uBAAuB;;AAEjD;AACA,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,WAAW,MAAM,2BAA2B;;AAEnD;AACA,OAAOC,kBAAkB,MAAM,iCAAiC;;AAEhE;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;;AAE5D;AACA,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,QAAQ,MAAM,kBAAkB;AAEvC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAG3C,WAAW,EAAE;EAC9B,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAMmD,WAAW,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACL,QAAQ,CAACM,MAAM,CAAC;EAC/D,MAAMC,YAAY,GAAGJ,WAAW,CAACK,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;EAErDvD,SAAS,CAAC,MAAM;IACdiD,UAAU,CAACK,YAAY,CAAC;EAC1B,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,MAAME,YAAY,GAAGT,QAAQ,CAACU,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC;EAE3D,oBACEd,OAAA;IAAAe,QAAA,gBACGf,OAAA,CAACrC,cAAc;MAACqD,QAAQ,EAAC,WAAW;MAACC,SAAS,EAAE;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAAG,eACzDrB,OAAA,CAACN,YAAY;MAAAqB,QAAA,eACXf,OAAA,CAACL,cAAc;QAAAoB,QAAA,GACZ,CAACH,YAAY,iBAAIZ,OAAA,CAACvC,MAAM;UAAC2C,OAAO,EAAEA,OAAQ;UAACC,UAAU,EAAEA;QAAW;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,eACtErB,OAAA;UAAMsB,SAAS,EAAEV,YAAY,GAAG,EAAE,GAAG,MAAO;UAAAG,QAAA,eAC1Cf,OAAA,CAACR,gBAAgB;YAAAuB,QAAA,eACff,OAAA,CAACP,YAAY;cAAAsB,QAAA,eACXf,OAAA,CAACJ,gBAAgB;gBAAAmB,QAAA,EACd,CAACH,YAAY,gBACZZ,OAAA,CAAC3C,SAAS;kBAAA0D,QAAA,eACRf,OAAA,CAACzC,MAAM;oBAAAwD,QAAA,gBACLf,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,GAAG;sBAACC,OAAO,eAAExB,OAAA,CAACpC,QAAQ;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,QAAI;sBAACI,KAAK;oBAAA;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eAC/CrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,SAAS;sBAACC,OAAO,eAAExB,OAAA,CAACxB,UAAU;wBAAC4B,OAAO,EAAEA;sBAAQ;wBAAAc,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eACnErB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,QAAQ;sBAACC,OAAO,eAAExB,OAAA,CAACjC,SAAS;wBAAAmD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eAC/CrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,SAAS;sBAACC,OAAO,eAAExB,OAAA,CAAC9B,MAAM;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eAC7CrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,WAAW;sBAACC,OAAO,eAAExB,OAAA,CAAChC,YAAY;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eACrDrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,UAAU;sBAACC,OAAO,eAAExB,OAAA,CAAC/B,WAAW;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eACnDrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,SAAS;sBAACC,OAAO,eAAExB,OAAA,CAACvB,UAAU;wBAAAyC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eACjDrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,eAAe;sBAACC,OAAO,eAAExB,OAAA,CAACnC,WAAW;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eACxDrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,aAAa;sBAACC,OAAO,eAAExB,OAAA,CAAC3B,gBAAgB;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eAC3DrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,UAAU;sBAACC,OAAO,eAAExB,OAAA,CAACzB,WAAW;wBAAA2C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eACnDrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,WAAW;sBAACC,OAAO,eAAExB,OAAA,CAAC7B,YAAY;wBAAA+C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eACrDrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,eAAe;sBAACC,OAAO,eAAExB,OAAA,CAAC1B,gBAAgB;wBAAA4C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eAC7DrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,aAAa;sBAACC,OAAO,eAAExB,OAAA,CAAC5B,eAAe;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eAC1DrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,OAAO;sBAACC,OAAO,eAAExB,OAAA,CAAClC,QAAQ;wBAAAoD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eAC7CrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,YAAY;sBAACC,OAAO,eAAExB,OAAA,CAACF,QAAQ;wBAAAoB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG,eAClDrB,OAAA,CAAC1C,KAAK;sBAACiE,IAAI,EAAC,YAAY;sBAACC,OAAO,eAAExB,OAAA,CAACtB,aAAa;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAI;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAG;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA;gBAChD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QACC,gBAEZrB,OAAA,CAACzC,MAAM;kBAAAwD,QAAA,gBACLf,OAAA,CAAC1C,KAAK;oBAACiE,IAAI,EAAC,QAAQ;oBAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;sBAAC6B,SAAS,EAAE,IAAK;sBAAAX,QAAA,eAACf,OAAA,CAACrB,cAAc;wBAAAuC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAAkB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAG,eACtGrB,OAAA,CAAC1C,KAAK;oBAACiE,IAAI,EAAC,iBAAiB;oBAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;sBAAC6B,SAAS,EAAE,IAAK;sBAAAX,QAAA,eAACf,OAAA,CAACpB,aAAa;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAAkB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAG,eAC9GrB,OAAA,CAAC1C,KAAK;oBAACiE,IAAI,EAAC,eAAe;oBAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;sBAAC6B,SAAS,EAAE,IAAK;sBAAAX,QAAA,eAACf,OAAA,CAACnB,WAAW;wBAAAqC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAAkB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAG,eAC1GrB,OAAA,CAAC1C,KAAK;oBAACiE,IAAI,EAAC,mBAAmB;oBAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;sBAAC6B,SAAS,EAAE,IAAK;sBAAAX,QAAA,eAACf,OAAA,CAAClB,eAAe;wBAAAoC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAAkB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAG,eAClHrB,OAAA,CAAC1C,KAAK;oBAACiE,IAAI,EAAC,cAAc;oBAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;sBAAC6B,SAAS,EAAE,IAAK;sBAAAX,QAAA,eAACf,OAAA,CAACjB,UAAU;wBAAAmC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAAkB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAG,eACxGrB,OAAA,CAAC1C,KAAK;oBAACiE,IAAI,EAAC,eAAe;oBAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;sBAAC6B,SAAS,EAAE,IAAK;sBAAAX,QAAA,eAACf,OAAA,CAAChB,WAAW;wBAAAkC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAAkB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAG,eAC1GrB,OAAA,CAAC1C,KAAK;oBAACiE,IAAI,EAAC,gBAAgB;oBAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;sBAAC6B,SAAS,EAAE,IAAK;sBAAAX,QAAA,eAACf,OAAA,CAACf,YAAY;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAAkB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAG,eAC5GrB,OAAA,CAAC1C,KAAK;oBAACiE,IAAI,EAAC,aAAa;oBAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;sBAAC6B,SAAS,EAAE,IAAK;sBAAAX,QAAA,eAACf,OAAA,CAACX,SAAS;wBAAA6B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAAkB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAG,eACtGrB,OAAA,CAAC1C,KAAK;oBAACiE,IAAI,EAAC,gBAAgB;oBAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;sBAAC6B,SAAS,EAAE,IAAK;sBAAAX,QAAA,eAACf,OAAA,CAACV,WAAW;wBAAA4B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAAkB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAG,eAC3GrB,OAAA,CAAC1C,KAAK;oBAACiE,IAAI,EAAC,eAAe;oBAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;sBAAC6B,SAAS,EAAE,IAAK;sBAAAX,QAAA,eAACf,OAAA,CAACd,WAAW;wBAAAgC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAAkB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAG,eAC1GrB,OAAA,CAAC1C,KAAK;oBAACiE,IAAI,EAAC,gBAAgB;oBAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;sBAAC6B,SAAS,EAAE,IAAK;sBAAAX,QAAA,eAACf,OAAA,CAACb,WAAW;wBAAA+B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAAkB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAG,eAC3GrB,OAAA,CAAC1C,KAAK;oBAACiE,IAAI,EAAC,gBAAgB;oBAACC,OAAO,eAAExB,OAAA,CAACH,cAAc;sBAAC6B,SAAS,EAAE,IAAK;sBAAAX,QAAA,eAACf,OAAA,CAACZ,YAAY;wBAAA8B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA;oBAAG;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA;kBAAkB;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAG;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA;cAE/G;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA;YACgB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QACE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACd,EACN,CAACT,YAAY,iBAAIZ,OAAA,CAACtC,MAAM;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,EAC3B,CAACT,YAAY,iBAAIZ,OAAA,CAACT,kBAAkB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACzB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACX;AAEV,CAAC;AAACnB,EAAA,CArEID,UAAU;EAAA,QACGzC,WAAW;AAAA;AAAAmE,EAAA,GADxB1B,UAAU;AAuEhB,SAAS2B,GAAGA,CAAA,EAAG;EACb,oBAAO5B,OAAA,CAACC,UAAU;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QAAG;AACvB;AAACQ,GAAA,GAFQD,GAAG;AAIZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}