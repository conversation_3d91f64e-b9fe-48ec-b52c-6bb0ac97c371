{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\FloatingChatButton.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext } from 'react';\nimport { Button } from 'react-bootstrap';\nimport { FaRobot, FaTimes } from 'react-icons/fa';\nimport AIChatbox from './AIChatbox';\nimport { UserContext } from '../contexts/userContext';\nimport './AIChatbox.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FloatingChatButton = () => {\n  _s();\n  const [showChatbox, setShowChatbox] = useState(false);\n  const {\n    userInfo\n  } = useContext(UserContext);\n  const handleToggleChatbox = () => {\n    setShowChatbox(!showChatbox);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Button, {\n      className: \"floating-chat-btn\",\n      onClick: handleToggleChatbox,\n      title: \"Tr\\u1EE3 l\\xFD AI\",\n      children: showChatbox ? /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 24\n      }, this) : /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 38\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AIChatbox, {\n      show: showChatbox,\n      onHide: () => setShowChatbox(false),\n      userInfo: userInfo\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(FloatingChatButton, \"wByNzaWMeV0NeNGgaOVyfXryQvs=\");\n_c = FloatingChatButton;\nexport default FloatingChatButton;\nvar _c;\n$RefreshReg$(_c, \"FloatingChatButton\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "<PERSON><PERSON>", "FaRobot", "FaTimes", "AIChatbox", "UserContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FloatingChatButton", "_s", "showChatbox", "setShowChatbox", "userInfo", "handleToggleChatbox", "children", "className", "onClick", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "show", "onHide", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/FloatingChatButton.jsx"], "sourcesContent": ["import React, { useState, useContext } from 'react';\nimport { But<PERSON> } from 'react-bootstrap';\nimport { FaRobot, FaTimes } from 'react-icons/fa';\nimport AIChatbox from './AIChatbox';\nimport { UserContext } from '../contexts/userContext';\nimport './AIChatbox.css';\n\nconst FloatingChatButton = () => {\n  const [showChatbox, setShowChatbox] = useState(false);\n  const { userInfo } = useContext(UserContext);\n\n  const handleToggleChatbox = () => {\n    setShowChatbox(!showChatbox);\n  };\n\n  return (\n    <>\n      <Button\n        className=\"floating-chat-btn\"\n        onClick={handleToggleChatbox}\n        title=\"Trợ lý AI\"\n      >\n        {showChatbox ? <FaTimes /> : <FaRobot />}\n      </Button>\n\n      <AIChatbox\n        show={showChatbox}\n        onHide={() => setShowChatbox(false)}\n        userInfo={userInfo}\n      />\n    </>\n  );\n};\n\nexport default FloatingChatButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AACjD,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM;IAAEe;EAAS,CAAC,GAAGd,UAAU,CAACK,WAAW,CAAC;EAE5C,MAAMU,mBAAmB,GAAGA,CAAA,KAAM;IAChCF,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC;EAED,oBACEL,OAAA,CAAAE,SAAA;IAAAO,QAAA,gBACET,OAAA,CAACN,MAAM;MACLgB,SAAS,EAAC,mBAAmB;MAC7BC,OAAO,EAAEH,mBAAoB;MAC7BI,KAAK,EAAC,mBAAW;MAAAH,QAAA,EAEhBJ,WAAW,gBAAGL,OAAA,CAACJ,OAAO;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG,gBAAGhB,OAAA,CAACL,OAAO;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAG;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACjC,eAEThB,OAAA,CAACH,SAAS;MACRoB,IAAI,EAAEZ,WAAY;MAClBa,MAAM,EAAEA,CAAA,KAAMZ,cAAc,CAAC,KAAK,CAAE;MACpCC,QAAQ,EAAEA;IAAS;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACnB;EAAA,gBACD;AAEP,CAAC;AAACZ,EAAA,CAzBID,kBAAkB;AAAAgB,EAAA,GAAlBhB,kBAAkB;AA2BxB,eAAeA,kBAAkB;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}