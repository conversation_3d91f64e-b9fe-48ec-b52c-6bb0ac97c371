{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\AIChatbox.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Modal, Button, Form, Card, Badge, Spinner } from 'react-bootstrap';\nimport { FaRobot, FaPaperPlane, FaTimes, FaUser } from 'react-icons/fa';\nimport './AIChatbox.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AIChatbox = _ref => {\n  _s();\n  let {\n    show,\n    onHide,\n    userInfo\n  } = _ref;\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [sessionId, setSessionId] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  useEffect(() => {\n    if (show && messages.length === 0) {\n      // Gửi tin nhắn chào mừng khi mở chatbox\n      handleWelcomeMessage();\n    }\n  }, [show]);\n  const handleWelcomeMessage = async () => {\n    const welcomeMessage = {\n      id: Date.now(),\n      type: 'ai',\n      content: `Xin chào${userInfo !== null && userInfo !== void 0 && userInfo.first_name ? ` ${userInfo.first_name}` : ''}! Tôi là trợ lý AI của shop. Tôi có thể giúp bạn tìm sản phẩm, chọn size, và hỗ trợ đặt hàng. Bạn cần hỗ trợ gì?`,\n      timestamp: new Date(),\n      quickReplies: ['Tìm sản phẩm', 'Hỗ trợ chọn size', 'Kiểm tra đơn hàng', 'Xem khuyến mãi']\n    };\n    setMessages([welcomeMessage]);\n  };\n  const sendMessage = async function () {\n    let message = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : inputMessage;\n    if (!message.trim()) return;\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: message,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n    setIsLoading(true);\n    try {\n      const token = localStorage.getItem('authTokens') ? JSON.parse(localStorage.getItem('authTokens')).access : null;\n      if (!token) {\n        const errorMessage = {\n          id: Date.now() + 1,\n          type: 'ai',\n          content: 'Bạn cần đăng nhập để sử dụng AI chatbox. Vui lòng đăng nhập và thử lại.',\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, errorMessage]);\n        setIsTyping(false);\n        setIsLoading(false);\n        return;\n      }\n      const response = await fetch('http://localhost:8000/ai/chat/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `JWT ${token}`\n        },\n        body: JSON.stringify({\n          message: message,\n          ...(sessionId && {\n            session_id: sessionId\n          }),\n          context: {}\n        })\n      });\n      console.log('Response status:', response.status);\n      console.log('Response headers:', response.headers);\n      if (response.ok) {\n        const data = await response.json();\n        if (!sessionId) {\n          setSessionId(data.session_id);\n        }\n        const aiMessage = {\n          id: Date.now() + 1,\n          type: 'ai',\n          content: data.message,\n          timestamp: new Date(),\n          quickReplies: data.quick_replies || [],\n          suggestedProducts: data.suggested_products || [],\n          actionsTaken: data.actions_taken || []\n        };\n        setMessages(prev => [...prev, aiMessage]);\n      } else {\n        const errorText = await response.text();\n        console.error('Response error:', errorText);\n        throw new Error(`Server error: ${response.status} - ${errorText}`);\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n      let errorContent = 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại sau.';\n      if (error.message.includes('Failed to fetch')) {\n        errorContent = 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.';\n      } else if (error.message.includes('401')) {\n        errorContent = 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.';\n      } else if (error.message.includes('400')) {\n        errorContent = 'Dữ liệu không hợp lệ. Vui lòng thử lại.';\n      } else if (error.message.includes('500')) {\n        errorContent = 'Lỗi server. Vui lòng thử lại sau.';\n      }\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'ai',\n        content: errorContent,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsTyping(false);\n      setIsLoading(false);\n    }\n  };\n  const handleQuickReply = reply => {\n    sendMessage(reply);\n  };\n  const handleProductClick = product => {\n    window.open(`/products/${product.id}`, '_blank');\n  };\n  const formatTime = timestamp => {\n    return new Date(timestamp).toLocaleTimeString('vi-VN', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    size: \"lg\",\n    className: \"ai-chatbox-modal\",\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      className: \"ai-chatbox-header\",\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), \"Tr\\u1EE3 l\\xFD AI\", /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"success\",\n          className: \"ms-2\",\n          children: \"Online\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      className: \"ai-chatbox-body\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"messages-container\",\n        children: [messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `message ${message.type}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-avatar\",\n            children: message.type === 'ai' ? /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 42\n            }, this) : /*#__PURE__*/_jsxDEV(FaUser, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 56\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-bubble\",\n              children: message.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-time\",\n              children: formatTime(message.timestamp)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this), message.quickReplies && message.quickReplies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quick-replies\",\n              children: message.quickReplies.map((reply, index) => /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-primary\",\n                size: \"sm\",\n                className: \"quick-reply-btn\",\n                onClick: () => handleQuickReply(reply),\n                children: reply\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this), message.suggestedProducts && message.suggestedProducts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"suggested-products\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"S\\u1EA3n ph\\u1EA9m g\\u1EE3i \\xFD:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"products-grid\",\n                children: message.suggestedProducts.slice(0, 4).map(product => /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"product-card\",\n                  onClick: () => handleProductClick(product),\n                  children: [/*#__PURE__*/_jsxDEV(Card.Img, {\n                    variant: \"top\",\n                    src: product.image || '/placeholder.png',\n                    className: \"product-image\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"p-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Card.Title, {\n                      className: \"product-name\",\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Card.Text, {\n                      className: \"product-price\",\n                      children: [parseInt(product.price).toLocaleString('vi-VN'), \" VND\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 27\n                  }, this)]\n                }, product.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)]\n        }, message.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this)), isTyping && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message ai\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-avatar\",\n            children: /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"typing-indicator\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      className: \"ai-chatbox-footer\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        className: \"message-form\",\n        onSubmit: e => {\n          e.preventDefault();\n          sendMessage();\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"input-group\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"text\",\n            placeholder: \"Nh\\u1EADp tin nh\\u1EAFn...\",\n            value: inputMessage,\n            onChange: e => setInputMessage(e.target.value),\n            disabled: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"primary\",\n            disabled: isLoading || !inputMessage.trim(),\n            children: isLoading ? /*#__PURE__*/_jsxDEV(Spinner, {\n              size: \"sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 28\n            }, this) : /*#__PURE__*/_jsxDEV(FaPaperPlane, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n_s(AIChatbox, \"RDb7+g1HuNvQktWvGA27XmDMGhQ=\");\n_c = AIChatbox;\nexport default AIChatbox;\nvar _c;\n$RefreshReg$(_c, \"AIChatbox\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Modal", "<PERSON><PERSON>", "Form", "Card", "Badge", "Spinner", "FaRobot", "FaPaperPlane", "FaTimes", "FaUser", "jsxDEV", "_jsxDEV", "AIChatbox", "_ref", "_s", "show", "onHide", "userInfo", "messages", "setMessages", "inputMessage", "setInputMessage", "isTyping", "setIsTyping", "sessionId", "setSessionId", "isLoading", "setIsLoading", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "length", "handleWelcomeMessage", "welcomeMessage", "id", "Date", "now", "type", "content", "first_name", "timestamp", "quickReplies", "sendMessage", "message", "arguments", "undefined", "trim", "userMessage", "prev", "token", "localStorage", "getItem", "JSON", "parse", "access", "errorMessage", "response", "fetch", "method", "headers", "body", "stringify", "session_id", "context", "console", "log", "status", "ok", "data", "json", "aiMessage", "quick_replies", "suggestedProducts", "suggested_products", "actionsTaken", "actions_taken", "errorText", "text", "error", "Error", "errorContent", "includes", "handleQuickReply", "reply", "handleProductClick", "product", "window", "open", "formatTime", "toLocaleTimeString", "hour", "minute", "size", "className", "children", "Header", "closeButton", "Title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "bg", "Body", "map", "index", "variant", "onClick", "slice", "Img", "src", "image", "name", "Text", "parseInt", "price", "toLocaleString", "ref", "Footer", "onSubmit", "e", "preventDefault", "Control", "placeholder", "value", "onChange", "target", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/AIChatbox.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Modal, Button, Form, Card, Badge, Spinner } from 'react-bootstrap';\nimport { FaRobot, FaPaperPlane, FaTimes, FaUser } from 'react-icons/fa';\nimport './AIChatbox.css';\n\nconst AIChatbox = ({ show, onHide, userInfo }) => {\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [sessionId, setSessionId] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    if (show && messages.length === 0) {\n      // G<PERSON>i tin nhắn chào mừng khi mở chatbox\n      handleWelcomeMessage();\n    }\n  }, [show]);\n\n  const handleWelcomeMessage = async () => {\n    const welcomeMessage = {\n      id: Date.now(),\n      type: 'ai',\n      content: `Xin chào${userInfo?.first_name ? ` ${userInfo.first_name}` : ''}! Tôi là trợ lý AI của shop. Tôi có thể giúp bạn tìm sản phẩm, chọn size, và hỗ trợ đặt hàng. Bạn cần hỗ trợ gì?`,\n      timestamp: new Date(),\n      quickReplies: ['Tìm sản phẩm', 'Hỗ trợ chọn size', 'Kiểm tra đơn hàng', 'Xem khuyến mãi']\n    };\n    setMessages([welcomeMessage]);\n  };\n\n  const sendMessage = async (message = inputMessage) => {\n    if (!message.trim()) return;\n\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: message,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n    setIsLoading(true);\n\n    try {\n      const token = localStorage.getItem('authTokens') ?\n        JSON.parse(localStorage.getItem('authTokens')).access : null;\n\n      if (!token) {\n        const errorMessage = {\n          id: Date.now() + 1,\n          type: 'ai',\n          content: 'Bạn cần đăng nhập để sử dụng AI chatbox. Vui lòng đăng nhập và thử lại.',\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, errorMessage]);\n        setIsTyping(false);\n        setIsLoading(false);\n        return;\n      }\n\n      const response = await fetch('http://localhost:8000/ai/chat/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `JWT ${token}`\n        },\n        body: JSON.stringify({\n          message: message,\n          ...(sessionId && { session_id: sessionId }),\n          context: {}\n        })\n      });\n\n      console.log('Response status:', response.status);\n      console.log('Response headers:', response.headers);\n\n      if (response.ok) {\n        const data = await response.json();\n        \n        if (!sessionId) {\n          setSessionId(data.session_id);\n        }\n\n        const aiMessage = {\n          id: Date.now() + 1,\n          type: 'ai',\n          content: data.message,\n          timestamp: new Date(),\n          quickReplies: data.quick_replies || [],\n          suggestedProducts: data.suggested_products || [],\n          actionsTaken: data.actions_taken || []\n        };\n\n        setMessages(prev => [...prev, aiMessage]);\n      } else {\n        const errorText = await response.text();\n        console.error('Response error:', errorText);\n        throw new Error(`Server error: ${response.status} - ${errorText}`);\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n\n      let errorContent = 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại sau.';\n\n      if (error.message.includes('Failed to fetch')) {\n        errorContent = 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.';\n      } else if (error.message.includes('401')) {\n        errorContent = 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.';\n      } else if (error.message.includes('400')) {\n        errorContent = 'Dữ liệu không hợp lệ. Vui lòng thử lại.';\n      } else if (error.message.includes('500')) {\n        errorContent = 'Lỗi server. Vui lòng thử lại sau.';\n      }\n\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'ai',\n        content: errorContent,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsTyping(false);\n      setIsLoading(false);\n    }\n  };\n\n  const handleQuickReply = (reply) => {\n    sendMessage(reply);\n  };\n\n  const handleProductClick = (product) => {\n    window.open(`/products/${product.id}`, '_blank');\n  };\n\n  const formatTime = (timestamp) => {\n    return new Date(timestamp).toLocaleTimeString('vi-VN', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <Modal show={show} onHide={onHide} size=\"lg\" className=\"ai-chatbox-modal\">\n      <Modal.Header closeButton className=\"ai-chatbox-header\">\n        <Modal.Title>\n          <FaRobot className=\"me-2\" />\n          Trợ lý AI\n          <Badge bg=\"success\" className=\"ms-2\">Online</Badge>\n        </Modal.Title>\n      </Modal.Header>\n      \n      <Modal.Body className=\"ai-chatbox-body\">\n        <div className=\"messages-container\">\n          {messages.map((message) => (\n            <div key={message.id} className={`message ${message.type}`}>\n              <div className=\"message-avatar\">\n                {message.type === 'ai' ? <FaRobot /> : <FaUser />}\n              </div>\n              \n              <div className=\"message-content\">\n                <div className=\"message-bubble\">\n                  {message.content}\n                </div>\n                \n                <div className=\"message-time\">\n                  {formatTime(message.timestamp)}\n                </div>\n\n                {/* Quick Replies */}\n                {message.quickReplies && message.quickReplies.length > 0 && (\n                  <div className=\"quick-replies\">\n                    {message.quickReplies.map((reply, index) => (\n                      <Button\n                        key={index}\n                        variant=\"outline-primary\"\n                        size=\"sm\"\n                        className=\"quick-reply-btn\"\n                        onClick={() => handleQuickReply(reply)}\n                      >\n                        {reply}\n                      </Button>\n                    ))}\n                  </div>\n                )}\n\n                {/* Suggested Products */}\n                {message.suggestedProducts && message.suggestedProducts.length > 0 && (\n                  <div className=\"suggested-products\">\n                    <h6>Sản phẩm gợi ý:</h6>\n                    <div className=\"products-grid\">\n                      {message.suggestedProducts.slice(0, 4).map((product) => (\n                        <Card \n                          key={product.id} \n                          className=\"product-card\"\n                          onClick={() => handleProductClick(product)}\n                        >\n                          <Card.Img \n                            variant=\"top\" \n                            src={product.image || '/placeholder.png'} \n                            className=\"product-image\"\n                          />\n                          <Card.Body className=\"p-2\">\n                            <Card.Title className=\"product-name\">\n                              {product.name}\n                            </Card.Title>\n                            <Card.Text className=\"product-price\">\n                              {parseInt(product.price).toLocaleString('vi-VN')} VND\n                            </Card.Text>\n                          </Card.Body>\n                        </Card>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n          \n          {isTyping && (\n            <div className=\"message ai\">\n              <div className=\"message-avatar\">\n                <FaRobot />\n              </div>\n              <div className=\"message-content\">\n                <div className=\"typing-indicator\">\n                  <span></span>\n                  <span></span>\n                  <span></span>\n                </div>\n              </div>\n            </div>\n          )}\n          \n          <div ref={messagesEndRef} />\n        </div>\n      </Modal.Body>\n      \n      <Modal.Footer className=\"ai-chatbox-footer\">\n        <Form className=\"message-form\" onSubmit={(e) => { e.preventDefault(); sendMessage(); }}>\n          <div className=\"input-group\">\n            <Form.Control\n              type=\"text\"\n              placeholder=\"Nhập tin nhắn...\"\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              disabled={isLoading}\n            />\n            <Button \n              type=\"submit\" \n              variant=\"primary\"\n              disabled={isLoading || !inputMessage.trim()}\n            >\n              {isLoading ? <Spinner size=\"sm\" /> : <FaPaperPlane />}\n            </Button>\n          </div>\n        </Form>\n      </Modal.Footer>\n    </Modal>\n  );\n};\n\nexport default AIChatbox;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AAC3E,SAASC,OAAO,EAAEC,YAAY,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AACvE,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGC,IAAA,IAAgC;EAAAC,EAAA;EAAA,IAA/B;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAAJ,IAAA;EAC3C,MAAM,CAACK,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM+B,cAAc,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAM8B,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDnC,SAAS,CAAC,MAAM;IACd+B,cAAc,EAAE;EAClB,CAAC,EAAE,CAACX,QAAQ,CAAC,CAAC;EAEdpB,SAAS,CAAC,MAAM;IACd,IAAIiB,IAAI,IAAIG,QAAQ,CAACgB,MAAM,KAAK,CAAC,EAAE;MACjC;MACAC,oBAAoB,EAAE;IACxB;EACF,CAAC,EAAE,CAACpB,IAAI,CAAC,CAAC;EAEV,MAAMoB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMC,cAAc,GAAG;MACrBC,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE;MACdC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAG,WAAUxB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEyB,UAAU,GAAI,IAAGzB,QAAQ,CAACyB,UAAW,EAAC,GAAG,EAAG,kHAAiH;MAC3LC,SAAS,EAAE,IAAIL,IAAI,EAAE;MACrBM,YAAY,EAAE,CAAC,cAAc,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,gBAAgB;IAC1F,CAAC;IACDzB,WAAW,CAAC,CAACiB,cAAc,CAAC,CAAC;EAC/B,CAAC;EAED,MAAMS,WAAW,GAAG,eAAAA,CAAA,EAAkC;IAAA,IAA3BC,OAAO,GAAAC,SAAA,CAAAb,MAAA,QAAAa,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG3B,YAAY;IAC/C,IAAI,CAAC0B,OAAO,CAACG,IAAI,EAAE,EAAE;IAErB,MAAMC,WAAW,GAAG;MAClBb,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE;MACdC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEK,OAAO;MAChBH,SAAS,EAAE,IAAIL,IAAI;IACrB,CAAC;IAEDnB,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,WAAW,CAAC,CAAC;IAC3C7B,eAAe,CAAC,EAAE,CAAC;IACnBE,WAAW,CAAC,IAAI,CAAC;IACjBI,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMyB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAC9CC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAACG,MAAM,GAAG,IAAI;MAE9D,IAAI,CAACL,KAAK,EAAE;QACV,MAAMM,YAAY,GAAG;UACnBrB,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC;UAClBC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE,yEAAyE;UAClFE,SAAS,EAAE,IAAIL,IAAI;QACrB,CAAC;QACDnB,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEO,YAAY,CAAC,CAAC;QAC5CnC,WAAW,CAAC,KAAK,CAAC;QAClBI,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;MAEA,MAAMgC,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,OAAMV,KAAM;QAChC,CAAC;QACDW,IAAI,EAAER,IAAI,CAACS,SAAS,CAAC;UACnBlB,OAAO,EAAEA,OAAO;UAChB,IAAItB,SAAS,IAAI;YAAEyC,UAAU,EAAEzC;UAAU,CAAC,CAAC;UAC3C0C,OAAO,EAAE,CAAC;QACZ,CAAC;MACH,CAAC,CAAC;MAEFC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAET,QAAQ,CAACU,MAAM,CAAC;MAChDF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAET,QAAQ,CAACG,OAAO,CAAC;MAElD,IAAIH,QAAQ,CAACW,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMZ,QAAQ,CAACa,IAAI,EAAE;QAElC,IAAI,CAAChD,SAAS,EAAE;UACdC,YAAY,CAAC8C,IAAI,CAACN,UAAU,CAAC;QAC/B;QAEA,MAAMQ,SAAS,GAAG;UAChBpC,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC;UAClBC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE8B,IAAI,CAACzB,OAAO;UACrBH,SAAS,EAAE,IAAIL,IAAI,EAAE;UACrBM,YAAY,EAAE2B,IAAI,CAACG,aAAa,IAAI,EAAE;UACtCC,iBAAiB,EAAEJ,IAAI,CAACK,kBAAkB,IAAI,EAAE;UAChDC,YAAY,EAAEN,IAAI,CAACO,aAAa,IAAI;QACtC,CAAC;QAED3D,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEsB,SAAS,CAAC,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMM,SAAS,GAAG,MAAMpB,QAAQ,CAACqB,IAAI,EAAE;QACvCb,OAAO,CAACc,KAAK,CAAC,iBAAiB,EAAEF,SAAS,CAAC;QAC3C,MAAM,IAAIG,KAAK,CAAE,iBAAgBvB,QAAQ,CAACU,MAAO,MAAKU,SAAU,EAAC,CAAC;MACpE;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAE9C,IAAIE,YAAY,GAAG,+CAA+C;MAElE,IAAIF,KAAK,CAACnC,OAAO,CAACsC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;QAC7CD,YAAY,GAAG,+DAA+D;MAChF,CAAC,MAAM,IAAIF,KAAK,CAACnC,OAAO,CAACsC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACxCD,YAAY,GAAG,qDAAqD;MACtE,CAAC,MAAM,IAAIF,KAAK,CAACnC,OAAO,CAACsC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACxCD,YAAY,GAAG,yCAAyC;MAC1D,CAAC,MAAM,IAAIF,KAAK,CAACnC,OAAO,CAACsC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACxCD,YAAY,GAAG,mCAAmC;MACpD;MAEA,MAAMzB,YAAY,GAAG;QACnBrB,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC;QAClBC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE0C,YAAY;QACrBxC,SAAS,EAAE,IAAIL,IAAI;MACrB,CAAC;MACDnB,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEO,YAAY,CAAC,CAAC;IAC9C,CAAC,SAAS;MACRnC,WAAW,CAAC,KAAK,CAAC;MAClBI,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM0D,gBAAgB,GAAIC,KAAK,IAAK;IAClCzC,WAAW,CAACyC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMC,kBAAkB,GAAIC,OAAO,IAAK;IACtCC,MAAM,CAACC,IAAI,CAAE,aAAYF,OAAO,CAACnD,EAAG,EAAC,EAAE,QAAQ,CAAC;EAClD,CAAC;EAED,MAAMsD,UAAU,GAAIhD,SAAS,IAAK;IAChC,OAAO,IAAIL,IAAI,CAACK,SAAS,CAAC,CAACiD,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACEnF,OAAA,CAACX,KAAK;IAACe,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAAC+E,IAAI,EAAC,IAAI;IAACC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBACvEtF,OAAA,CAACX,KAAK,CAACkG,MAAM;MAACC,WAAW;MAACH,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eACrDtF,OAAA,CAACX,KAAK,CAACoG,KAAK;QAAAH,QAAA,gBACVtF,OAAA,CAACL,OAAO;UAAC0F,SAAS,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,qBAE5B,eAAA7F,OAAA,CAACP,KAAK;UAACqG,EAAE,EAAC,SAAS;UAACT,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAQ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACvC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD,eAEf7F,OAAA,CAACX,KAAK,CAAC0G,IAAI;MAACV,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eACrCtF,OAAA;QAAKqF,SAAS,EAAC,oBAAoB;QAAAC,QAAA,GAChC/E,QAAQ,CAACyF,GAAG,CAAE7D,OAAO,iBACpBnC,OAAA;UAAsBqF,SAAS,EAAG,WAAUlD,OAAO,CAACN,IAAK,EAAE;UAAAyD,QAAA,gBACzDtF,OAAA;YAAKqF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BnD,OAAO,CAACN,IAAI,KAAK,IAAI,gBAAG7B,OAAA,CAACL,OAAO;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAG,gBAAG7F,OAAA,CAACF,MAAM;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAC7C,eAEN7F,OAAA;YAAKqF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BtF,OAAA;cAAKqF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5BnD,OAAO,CAACL;YAAO;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACZ,eAEN7F,OAAA;cAAKqF,SAAS,EAAC,cAAc;cAAAC,QAAA,EAC1BN,UAAU,CAAC7C,OAAO,CAACH,SAAS;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAC1B,EAGL1D,OAAO,CAACF,YAAY,IAAIE,OAAO,CAACF,YAAY,CAACV,MAAM,GAAG,CAAC,iBACtDvB,OAAA;cAAKqF,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3BnD,OAAO,CAACF,YAAY,CAAC+D,GAAG,CAAC,CAACrB,KAAK,EAAEsB,KAAK,kBACrCjG,OAAA,CAACV,MAAM;gBAEL4G,OAAO,EAAC,iBAAiB;gBACzBd,IAAI,EAAC,IAAI;gBACTC,SAAS,EAAC,iBAAiB;gBAC3Bc,OAAO,EAAEA,CAAA,KAAMzB,gBAAgB,CAACC,KAAK,CAAE;gBAAAW,QAAA,EAEtCX;cAAK,GANDsB,KAAK;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAQb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAEL,EAGA1D,OAAO,CAAC6B,iBAAiB,IAAI7B,OAAO,CAAC6B,iBAAiB,CAACzC,MAAM,GAAG,CAAC,iBAChEvB,OAAA;cAAKqF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjCtF,OAAA;gBAAAsF,QAAA,EAAI;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,eACxB7F,OAAA;gBAAKqF,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3BnD,OAAO,CAAC6B,iBAAiB,CAACoC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACJ,GAAG,CAAEnB,OAAO,iBACjD7E,OAAA,CAACR,IAAI;kBAEH6F,SAAS,EAAC,cAAc;kBACxBc,OAAO,EAAEA,CAAA,KAAMvB,kBAAkB,CAACC,OAAO,CAAE;kBAAAS,QAAA,gBAE3CtF,OAAA,CAACR,IAAI,CAAC6G,GAAG;oBACPH,OAAO,EAAC,KAAK;oBACbI,GAAG,EAAEzB,OAAO,CAAC0B,KAAK,IAAI,kBAAmB;oBACzClB,SAAS,EAAC;kBAAe;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACzB,eACF7F,OAAA,CAACR,IAAI,CAACuG,IAAI;oBAACV,SAAS,EAAC,KAAK;oBAAAC,QAAA,gBACxBtF,OAAA,CAACR,IAAI,CAACiG,KAAK;sBAACJ,SAAS,EAAC,cAAc;sBAAAC,QAAA,EACjCT,OAAO,CAAC2B;oBAAI;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACF,eACb7F,OAAA,CAACR,IAAI,CAACiH,IAAI;sBAACpB,SAAS,EAAC,eAAe;sBAAAC,QAAA,GACjCoB,QAAQ,CAAC7B,OAAO,CAAC8B,KAAK,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC,EAAC,MACnD;oBAAA;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAY;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACF;gBAAA,GAhBPhB,OAAO,CAACnD,EAAE;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAkBlB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAET;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACG;QAAA,GA5DE1D,OAAO,CAACT,EAAE;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QA8DrB,CAAC,EAEDlF,QAAQ,iBACPX,OAAA;UAAKqF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBtF,OAAA;YAAKqF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BtF,OAAA,CAACL,OAAO;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACP,eACN7F,OAAA;YAAKqF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BtF,OAAA;cAAKqF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BtF,OAAA;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAa,eACb7F,OAAA;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAa,eACb7F,OAAA;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAa;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAET,eAED7F,OAAA;UAAK6G,GAAG,EAAE5F;QAAe;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACxB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACK,eAEb7F,OAAA,CAACX,KAAK,CAACyH,MAAM;MAACzB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eACzCtF,OAAA,CAACT,IAAI;QAAC8F,SAAS,EAAC,cAAc;QAAC0B,QAAQ,EAAGC,CAAC,IAAK;UAAEA,CAAC,CAACC,cAAc,EAAE;UAAE/E,WAAW,EAAE;QAAE,CAAE;QAAAoD,QAAA,eACrFtF,OAAA;UAAKqF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BtF,OAAA,CAACT,IAAI,CAAC2H,OAAO;YACXrF,IAAI,EAAC,MAAM;YACXsF,WAAW,EAAC,4BAAkB;YAC9BC,KAAK,EAAE3G,YAAa;YACpB4G,QAAQ,EAAGL,CAAC,IAAKtG,eAAe,CAACsG,CAAC,CAACM,MAAM,CAACF,KAAK,CAAE;YACjDG,QAAQ,EAAExG;UAAU;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACpB,eACF7F,OAAA,CAACV,MAAM;YACLuC,IAAI,EAAC,QAAQ;YACbqE,OAAO,EAAC,SAAS;YACjBqB,QAAQ,EAAExG,SAAS,IAAI,CAACN,YAAY,CAAC6B,IAAI,EAAG;YAAAgD,QAAA,EAE3CvE,SAAS,gBAAGf,OAAA,CAACN,OAAO;cAAC0F,IAAI,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAG,gBAAG7F,OAAA,CAACJ,YAAY;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAC9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACM;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACT;AAEZ,CAAC;AAAC1F,EAAA,CA1QIF,SAAS;AAAAuH,EAAA,GAATvH,SAAS;AA4Qf,eAAeA,SAAS;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}