from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsA<PERSON><PERSON><PERSON><PERSON>, IsAdminUser
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db.models import Count, Q
from django.core.paginator import Paginator
import uuid
import logging

from .models import AIConversation, AIMessage, AIAction, UserPreference, AIKnowledgeBase
from .serializers import (
    AIConversationSerializer, AIMessageSerializer, ChatRequestSerializer,
    ChatResponseSerializer, UserPreferenceSerializer, AIKnowledgeBaseSerializer
)
from .ai_service import AIResponseGenerator, AIProductSearchService, AISizeRecommendationService
from api.models import Product
from api.serializers import ProductSerializer

logger = logging.getLogger(__name__)


@api_view(['GET'])
def test_ai_endpoint(request):
    """Test endpoint để kiểm tra AI chat có hoạt động không"""
    return Response({
        'status': 'success',
        'message': 'AI Chat endpoint is working!',
        'user': request.user.username if request.user.is_authenticated else 'Anonymous'
    })


class AIChatView(APIView):
    """Main API endpoint cho AI chatbox"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Gửi tin nhắn đến AI và nhận phản hồi"""
        logger.info(f"AI Chat request from user: {request.user}")
        logger.info(f"Request data: {request.data}")

        serializer = ChatRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.error(f"Serializer validation errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        user_message = serializer.validated_data['message']
        session_id = serializer.validated_data.get('session_id')
        context = serializer.validated_data.get('context', {})

        # Handle null or empty session_id
        if not session_id or session_id == 'null':
            session_id = None
        
        try:
            # Tạo session_id nếu chưa có
            if not session_id:
                session_id = str(uuid.uuid4())

            # Tạo phản hồi AI đơn giản (không cần database)
            ai_response = self._generate_simple_response(user_message, session_id)

            # Tạo response
            response_data = {
                'message': ai_response['message'],
                'session_id': session_id,
                'message_type': 'ai',
                'actions_taken': ai_response.get('actions_taken', []),
                'suggested_products': ai_response.get('suggested_products', []),
                'quick_replies': ai_response.get('quick_replies', []),
                'metadata': ai_response.get('metadata', {})
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error in AI chat: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return Response(
                {'error': 'Có lỗi xảy ra khi xử lý tin nhắn. Vui lòng thử lại.'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _generate_simple_response(self, message, session_id):
        """Tạo phản hồi AI đơn giản không cần database"""
        message_lower = message.lower()

        # Phản hồi dựa trên từ khóa
        if any(word in message_lower for word in ['xin chào', 'hello', 'hi', 'chào']):
            return {
                'message': f'Xin chào! Tôi là trợ lý AI của shop. Tôi có thể giúp bạn tìm sản phẩm, chọn size, và hỗ trợ đặt hàng. Bạn cần hỗ trợ gì?',
                'quick_replies': ['Tìm sản phẩm', 'Hỗ trợ chọn size', 'Kiểm tra đơn hàng', 'Xem khuyến mãi'],
                'actions_taken': [{'type': 'greeting', 'message': message}],
                'metadata': {'intent': 'greeting'}
            }

        elif any(word in message_lower for word in ['tìm', 'search', 'sản phẩm', 'áo', 'quần', 'giày']):
            return {
                'message': 'Tôi có thể giúp bạn tìm sản phẩm! Bạn đang tìm loại sản phẩm nào? Hãy mô tả chi tiết hơn về màu sắc, kích thước, hoặc giá cả mong muốn.',
                'quick_replies': ['Áo thun', 'Quần jean', 'Giày sneaker', 'Phụ kiện'],
                'actions_taken': [{'type': 'product_search', 'query': message}],
                'metadata': {'intent': 'product_search'}
            }

        elif any(word in message_lower for word in ['size', 'cỡ', 'số', 'kích thước']):
            return {
                'message': 'Tôi có thể hỗ trợ bạn chọn size phù hợp! Bạn đang quan tâm đến sản phẩm nào? Tôi sẽ cung cấp bảng size chi tiết và hướng dẫn đo size.',
                'quick_replies': ['Size áo', 'Size quần', 'Size giày', 'Hướng dẫn đo'],
                'actions_taken': [{'type': 'size_help', 'query': message}],
                'metadata': {'intent': 'size_help'}
            }

        elif any(word in message_lower for word in ['khuyến mãi', 'sale', 'giảm giá', 'ưu đãi']):
            return {
                'message': 'Hiện tại chúng tôi có nhiều chương trình khuyến mãi hấp dẫn! Giảm giá lên đến 50% cho các sản phẩm thời trang. Bạn có thể xem thêm tại trang chủ hoặc đăng ký nhận thông báo.',
                'quick_replies': ['Xem khuyến mãi', 'Đăng ký nhận tin', 'Sản phẩm sale', 'Mã giảm giá'],
                'actions_taken': [{'type': 'promotion_inquiry', 'query': message}],
                'metadata': {'intent': 'promotion'}
            }

        elif any(word in message_lower for word in ['đặt hàng', 'order', 'mua', 'thanh toán']):
            return {
                'message': 'Tôi có thể hướng dẫn bạn đặt hàng! Quy trình rất đơn giản: Chọn sản phẩm → Thêm vào giỏ → Điền thông tin → Thanh toán. Bạn cần hỗ trợ bước nào?',
                'quick_replies': ['Hướng dẫn đặt hàng', 'Phương thức thanh toán', 'Kiểm tra giỏ hàng', 'Theo dõi đơn'],
                'actions_taken': [{'type': 'order_help', 'query': message}],
                'metadata': {'intent': 'order_help'}
            }

        else:
            return {
                'message': 'Cảm ơn bạn đã liên hệ! Tôi có thể giúp bạn tìm sản phẩm, chọn size, hỗ trợ đặt hàng, và trả lời các câu hỏi về shop. Bạn cần hỗ trợ gì cụ thể?',
                'quick_replies': ['Tìm sản phẩm', 'Hỗ trợ chọn size', 'Hướng dẫn đặt hàng', 'Liên hệ hỗ trợ'],
                'actions_taken': [{'type': 'general_help', 'query': message}],
                'metadata': {'intent': 'general'}
            }


class ConversationHistoryView(APIView):
    """Lấy lịch sử hội thoại"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request, session_id=None):
        """Lấy lịch sử hội thoại theo session_id hoặc tất cả conversations của user"""
        if session_id:
            conversation = get_object_or_404(AIConversation, session_id=session_id, user=request.user)
            serializer = AIConversationSerializer(conversation)
            return Response(serializer.data)
        else:
            conversations = AIConversation.objects.filter(user=request.user)[:10]
            serializer = AIConversationSerializer(conversations, many=True)
            return Response(serializer.data)


class ProductRecommendationView(APIView):
    """API để lấy gợi ý sản phẩm"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Lấy gợi ý sản phẩm dựa trên query"""
        query = request.data.get('query', '')
        limit = request.data.get('limit', 10)
        
        if not query:
            return Response({'error': 'Query is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            products = AIProductSearchService.search_products(query, request.user, limit)
            serializer = ProductSerializer(products, many=True)
            return Response({
                'products': serializer.data,
                'count': len(products),
                'query': query
            })
        except Exception as e:
            logger.error(f"Error in product recommendation: {str(e)}")
            return Response(
                {'error': 'Có lỗi xảy ra khi tìm kiếm sản phẩm'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SizeRecommendationView(APIView):
    """API để lấy gợi ý size"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Lấy gợi ý size cho sản phẩm"""
        product_id = request.data.get('product_id')
        user_info = request.data.get('user_info', {})
        
        if not product_id:
            return Response({'error': 'Product ID is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            product = get_object_or_404(Product, id=product_id)
            recommendations = AISizeRecommendationService.recommend_size(
                product, 
                request.user, 
                user_info
            )
            return Response(recommendations)
        except Exception as e:
            logger.error(f"Error in size recommendation: {str(e)}")
            return Response(
                {'error': 'Có lỗi xảy ra khi gợi ý size'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserPreferenceView(APIView):
    """API để quản lý sở thích user"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Lấy sở thích của user"""
        try:
            preference = UserPreference.objects.get(user=request.user)
            serializer = UserPreferenceSerializer(preference)
            return Response(serializer.data)
        except UserPreference.DoesNotExist:
            return Response({
                'preferred_brands': [],
                'preferred_categories': [],
                'size_preferences': {},
                'price_range': {},
                'style_preferences': []
            })
    
    def post(self, request):
        """Cập nhật sở thích của user"""
        try:
            preference, created = UserPreference.objects.get_or_create(user=request.user)
            serializer = UserPreferenceSerializer(preference, data=request.data, partial=True)
            
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error updating user preferences: {str(e)}")
            return Response(
                {'error': 'Có lỗi xảy ra khi cập nhật sở thích'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def quick_action(request):
    """Xử lý các quick actions từ chatbox"""
    action_type = request.data.get('action_type')
    parameters = request.data.get('parameters', {})
    
    try:
        if action_type == 'view_product':
            product_id = parameters.get('product_id')
            product = get_object_or_404(Product, id=product_id)
            serializer = ProductSerializer(product)
            return Response({
                'action': 'view_product',
                'product': serializer.data
            })
        
        elif action_type == 'add_to_cart':
            # Logic thêm vào giỏ hàng
            return Response({
                'action': 'add_to_cart',
                'message': 'Đã thêm sản phẩm vào giỏ hàng'
            })
        
        elif action_type == 'check_stock':
            product_id = parameters.get('product_id')
            color_id = parameters.get('color_id')
            size_id = parameters.get('size_id')
            
            # Logic kiểm tra tồn kho
            return Response({
                'action': 'check_stock',
                'in_stock': True,
                'quantity': 10
            })
        
        else:
            return Response(
                {'error': 'Unknown action type'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
    except Exception as e:
        logger.error(f"Error in quick action: {str(e)}")
        return Response(
            {'error': 'Có lỗi xảy ra khi thực hiện action'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Admin Views
class AdminChatStatsView(APIView):
    """API để lấy thống kê chat cho admin"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Lấy thống kê tổng quan"""
        try:
            # Thống kê cơ bản
            total_conversations = AIConversation.objects.count()
            total_messages = AIMessage.objects.count()
            active_conversations = AIConversation.objects.filter(is_active=True).count()

            # Thống kê theo ngày (7 ngày gần nhất)
            from datetime import datetime, timedelta
            last_7_days = datetime.now() - timedelta(days=7)

            daily_stats = []
            for i in range(7):
                date = last_7_days + timedelta(days=i)
                conversations_count = AIConversation.objects.filter(
                    created_at__date=date.date()
                ).count()
                messages_count = AIMessage.objects.filter(
                    timestamp__date=date.date()
                ).count()

                daily_stats.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'conversations': conversations_count,
                    'messages': messages_count
                })

            # Top actions
            top_actions = AIAction.objects.values('action_type').annotate(
                count=Count('id')
            ).order_by('-count')[:5]

            return Response({
                'total_conversations': total_conversations,
                'total_messages': total_messages,
                'active_conversations': active_conversations,
                'daily_stats': daily_stats,
                'top_actions': list(top_actions)
            })

        except Exception as e:
            logger.error(f"Error getting chat stats: {str(e)}")
            return Response(
                {'error': 'Có lỗi xảy ra khi lấy thống kê'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AdminConversationListView(APIView):
    """API để lấy danh sách conversations cho admin"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Lấy danh sách conversations với phân trang"""
        try:
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            search = request.GET.get('search', '')

            conversations = AIConversation.objects.select_related('user').order_by('-updated_at')

            if search:
                conversations = conversations.filter(
                    Q(user__username__icontains=search) |
                    Q(user__email__icontains=search) |
                    Q(session_id__icontains=search)
                )

            paginator = Paginator(conversations, page_size)
            page_obj = paginator.get_page(page)

            serializer = AIConversationSerializer(page_obj.object_list, many=True)

            return Response({
                'conversations': serializer.data,
                'total_pages': paginator.num_pages,
                'current_page': page,
                'total_count': paginator.count
            })

        except Exception as e:
            logger.error(f"Error getting conversations: {str(e)}")
            return Response(
                {'error': 'Có lỗi xảy ra khi lấy danh sách conversations'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AdminKnowledgeBaseView(APIView):
    """API để quản lý knowledge base"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Lấy danh sách knowledge base"""
        try:
            knowledge_type = request.GET.get('type', '')
            search = request.GET.get('search', '')

            knowledge = AIKnowledgeBase.objects.all().order_by('-created_at')

            if knowledge_type:
                knowledge = knowledge.filter(knowledge_type=knowledge_type)

            if search:
                knowledge = knowledge.filter(
                    Q(question__icontains=search) |
                    Q(answer__icontains=search)
                )

            serializer = AIKnowledgeBaseSerializer(knowledge, many=True)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error getting knowledge base: {str(e)}")
            return Response(
                {'error': 'Có lỗi xảy ra khi lấy knowledge base'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def post(self, request):
        """Tạo knowledge base mới"""
        try:
            serializer = AIKnowledgeBaseSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error creating knowledge base: {str(e)}")
            return Response(
                {'error': 'Có lỗi xảy ra khi tạo knowledge base'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AdminKnowledgeBaseDetailView(APIView):
    """API để quản lý chi tiết knowledge base"""
    permission_classes = [IsAdminUser]

    def put(self, request, pk):
        """Cập nhật knowledge base"""
        try:
            knowledge = get_object_or_404(AIKnowledgeBase, pk=pk)
            serializer = AIKnowledgeBaseSerializer(knowledge, data=request.data, partial=True)

            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error updating knowledge base: {str(e)}")
            return Response(
                {'error': 'Có lỗi xảy ra khi cập nhật knowledge base'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def delete(self, request, pk):
        """Xóa knowledge base"""
        try:
            knowledge = get_object_or_404(AIKnowledgeBase, pk=pk)
            knowledge.delete()
            return Response({'message': 'Đã xóa thành công'})

        except Exception as e:
            logger.error(f"Error deleting knowledge base: {str(e)}")
            return Response(
                {'error': 'Có lỗi xảy ra khi xóa knowledge base'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
