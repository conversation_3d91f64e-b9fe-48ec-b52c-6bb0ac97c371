/* Search Page Styles */
.search-page {
  padding: 20px 0;
  background-color: #f8f9fa;
  font-family: 'Inter', sans-serif;
}

.search-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0;
  color: #000;
  letter-spacing: 1px;
}

.search-results-count {
  color: #666;
  margin-bottom: 20px;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

/* Filter Sidebar */
.filter-sidebar {
  background-color: #222222;
  border: none;
  border-radius: 0;
  padding: 30px 25px;
  margin-bottom: 30px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  color: #f5f5f5;
}

.filter-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(to bottom, #ff3e3e, #ff7676);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  border-bottom: 1px solid #3a3a3a;
  padding-bottom: 15px;
}

.filter-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 2px;
  color: #fff;
}

.clear-filters {
  color: #bbb;
  text-decoration: none;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
  transition: all 0.3s;
}

.clear-filters:hover {
  color: #fff;
  text-decoration: underline;
}

.filter-section {
  margin-bottom: 25px;
  border-bottom: 1px solid #3a3a3a;
  padding-bottom: 20px;
}

.filter-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.filter-section h4 {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s;
}

.filter-section h4:hover {
  color: #ccc;
}

.filter-section h4::after {
  content: "+";
  font-size: 1rem;
  font-weight: 300;
  color: #bbb;
}

.filter-section.open h4::after {
  content: "−";
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-option {
  display: flex;
  align-items: center;
}

.filter-option input[type="checkbox"],
.filter-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.filter-option label {
  margin: 0;
  font-size: 0.85rem;
  color: #e0e0e0;
  cursor: pointer;
  padding-left: 28px;
  position: relative;
  display: flex;
  align-items: center;
  transition: all 0.3s;
}

.filter-option label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: 1px solid #666;
  background-color: transparent;
  transition: all 0.3s;
}

.filter-option input[type="checkbox"]:checked + label::before,
.filter-option input[type="radio"]:checked + label::before {
  border-color: #fff;
  background-color: #fff;
}

.filter-option input[type="checkbox"]:checked + label::after {
  content: '✓';
  position: absolute;
  left: 3px;
  top: 50%;
  transform: translateY(-50%);
  color: #000;
  font-size: 12px;
}

.filter-option input[type="radio"]:checked + label::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #000;
}

.filter-option label:hover {
  color: #fff;
}

.filter-option label:hover::before {
  border-color: #fff;
}

/* Price Range Slider */
.price-range {
  margin: 20px 0;
}

.price-slider {
  width: 100%;
  margin: 15px 0;
  -webkit-appearance: none;
  height: 2px;
  background: #666;
  outline: none;
}

.price-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #fff;
  cursor: pointer;
  border: 2px solid #000;
  box-shadow: 0 0 0 1px #fff;
}

.price-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #fff;
  cursor: pointer;
  border: 2px solid #000;
  box-shadow: 0 0 0 1px #fff;
}

.price-inputs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.price-inputs span {
  font-size: 0.85rem;
  color: #e0e0e0;
  font-weight: 500;
}

/* Color Filter */
.color-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.color-option {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  cursor: pointer;
  border: 1px solid #ddd;
  transition: all 0.3s;
  position: relative;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.selected {
  box-shadow: 0 0 0 2px #000;
}

.color-option.selected::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 14px;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

/* Size Filter */
.size-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.size-option {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 0;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  min-width: 40px;
}

.size-option:hover {
  border-color: #545252;
}

.size-option.selected {
  background-color: #4b4949;
  color: white;
  border-color: #525252;
}

/* Apply Filter Button */
.apply-filter {
  width: 100%;
  background-color: #fff;
  color: #000;
  border: none;
  padding: 12px;
  border-radius: 0;
  font-weight: 600;
  margin-top: 20px;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 2px;
  transition: all 0.3s;
}

.apply-filter:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.apply-filter:active {
  transform: translateY(0);
}

/* Products Grid */
.products-grid {
  margin-top: 20px;
}

.product-card {
  background-color: white;
  border-radius: 0;
  overflow: hidden;
  transition: transform 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-image {
  position: relative;
  padding-top: 125%;
  overflow: hidden;
  display: block;
}

.product-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  font-size: 0.7rem;
  font-weight: 600;
  border-radius: 2px;
}

.bestseller {
  background-color: #ff6b6b;
  color: white;
}

.product-info {
  padding: 15px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 0.9rem;
  color: #000;
  text-decoration: none;
  margin-bottom: 8px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.product-price {
  font-weight: 600;
  font-size: 0.95rem;
}

.product-rating {
  font-size: 0.8rem;
  color: #666;
  display: flex;
  align-items: center;
}

.product-rating i {
  color: #ffc107;
  margin-right: 3px;
}

.product-sold {
  margin-left: 8px;
  font-size: 0.75rem;
  color: #888;
}

.product-actions {
  margin-top: auto;
}

.btn-view {
  display: block;
  text-align: center;
  padding: 8px;
  background-color: #f8f9fa;
  color: #000;
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.btn-view:hover {
  background-color: #e9ecef;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.pagination .page-item {
  margin: 0 5px;
}

.pagination .page-link {
  color: #000;
  border-color: #ddd;
  border-radius: 0;
}

.pagination .page-item.active .page-link {
  background-color: #000;
  border-color: #000;
}

/* No Products */
.no-products {
  text-align: center;
  padding: 40px 0;
}

.no-products i {
  font-size: 3rem;
  color: #ddd;
  margin-bottom: 15px;
}

.no-products p {
  color: #666;
  font-size: 1rem;
}

/* Sort Options */
.sort-container {
  background-color: white;
  padding: 20px 25px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  position: relative;
}

.sort-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #000, #333);
}

.sort-options {
  display: flex;
  align-items: center;
}

.sort-label {
  font-size: 0.9rem;
  font-weight: 500;
  margin-right: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: #666;
}

.sort-select {
  padding: 8px 30px 8px 12px;
  border: 1px solid #ddd;
  border-radius: 0;
  font-size: 0.85rem;
  background-color: white;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23333' d='M0 0l4 4 4-4z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 8px;
  transition: all 0.3s;
}

.sort-select:focus {
  outline: none;
  border-color: #000;
}

/* Responsive */
@media (max-width: 768px) {
  .filter-sidebar {
    margin-bottom: 20px;
    padding: 20px 15px;
  }
  
  .sort-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .sort-options {
    margin-top: 15px;
    width: 100%;
  }
  
  .sort-select {
    width: 100%;
  }
  
  .search-title {
    font-size: 1.3rem;
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .filter-sidebar, .sort-container {
    background-color: #1e1e1e;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  }
  
  .filter-sidebar::before, .sort-container::before {
    background: linear-gradient(to bottom, #fff, #999);
  }
  
  .filter-header {
    border-bottom-color: #333;
  }
  
  .filter-header h3, .search-title {
    color: #fff;
  }
  
  .clear-filters {
    color: #aaa;
  }
  
  .clear-filters:hover {
    color: #fff;
  }
  
  .filter-section {
    border-bottom-color: #333;
  }
  
  .filter-section h4 {
    color: #fff;
  }
  
  .filter-section h4:hover {
    color: #ccc;
  }
  
  .filter-option label {
    color: #ccc;
  }
  
  .filter-option label::before {
    border-color: #555;
    background-color: #2c2c2c;
  }
  
  .filter-option input[type="radio"]:checked + label::before {
    border-color: #fff;
    background-color: #fff;
  }
  
  .filter-option input[type="radio"]:checked + label::after {
    background-color: #000;
  }
  
  .filter-option label:hover {
    color: #fff;
  }
  
  .filter-option label:hover::before {
    border-color: #fff;
  }
  
  .price-slider {
    background: #555;
  }
  
  .price-slider::-webkit-slider-thumb {
    background: #fff;
    border: 2px solid #000;
    box-shadow: 0 0 0 1px #fff;
  }
  
  .price-slider::-moz-range-thumb {
    background: #fff;
    border: 2px solid #000;
    box-shadow: 0 0 0 1px #fff;
  }
  
  .price-inputs span {
    color: #aaa;
  }
  
  .color-option {
    border-color: #555;
  }
  
  .color-option.selected {
    box-shadow: 0 0 0 2px #fff;
  }
  
  .size-option {
    border-color: #555;
    color: #ccc;
    background-color: #2c2c2c;
  }
  
  .size-option:hover {
    border-color: #fff;
  }
  
  .size-option.selected {
    background-color: #fff;
    color: #000;
    border-color: #fff;
  }
  
  .apply-filter {
    background-color: #fff;
    color: #000;
  }
  
  .apply-filter:hover {
    background-color: #ccc;
  }
  
  .sort-label {
    color: #aaa;
  }
  
  .sort-select {
    border-color: #555;
    background-color: #2c2c2c;
    color: #fff;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M0 0l4 4 4-4z'/%3E%3C/svg%3E");
  }
  
  .sort-select:focus {
    border-color: #fff;
  }
  
  .search-results-count {
    color: #aaa;
  }
}
