# Django Settings
DEBUG=False
SECRET_KEY=your-very-secret-key-here-change-this-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/ecommerce_db

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key

# Email Configuration (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# AI Chat Configuration (Optional)
OPENAI_API_KEY=your-openai-api-key-if-using-openai

# Security Settings
SECURE_SSL_REDIRECT=False
SECURE_PROXY_SSL_HEADER=None
CSRF_TRUSTED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000