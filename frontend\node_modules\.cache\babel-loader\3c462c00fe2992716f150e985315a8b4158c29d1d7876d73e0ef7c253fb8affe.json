{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\AIChatbox.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Modal, Button, Form, Card, Badge, Spinner } from 'react-bootstrap';\nimport { FaRobot, FaPaperPlane, FaTimes, FaUser } from 'react-icons/fa';\nimport './AIChatbox.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AIChatbox = _ref => {\n  _s();\n  let {\n    show,\n    onHide,\n    userInfo\n  } = _ref;\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [sessionId, setSessionId] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  useEffect(() => {\n    if (show && messages.length === 0) {\n      // Gửi tin nhắn chào mừng khi mở chatbox\n      handleWelcomeMessage();\n    }\n  }, [show]);\n  const handleWelcomeMessage = async () => {\n    const welcomeMessage = {\n      id: Date.now(),\n      type: 'ai',\n      content: `Xin chào${userInfo !== null && userInfo !== void 0 && userInfo.first_name ? ` ${userInfo.first_name}` : ''}! Tôi là trợ lý AI của shop. Tôi có thể giúp bạn tìm sản phẩm, chọn size, và hỗ trợ đặt hàng. Bạn cần hỗ trợ gì?`,\n      timestamp: new Date(),\n      quickReplies: ['Tìm sản phẩm', 'Hỗ trợ chọn size', 'Kiểm tra đơn hàng', 'Xem khuyến mãi']\n    };\n    setMessages([welcomeMessage]);\n  };\n  const sendMessage = async function () {\n    let message = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : inputMessage;\n    if (!message.trim()) return;\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: message,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n    setIsLoading(true);\n    try {\n      const token = localStorage.getItem('authTokens') ? JSON.parse(localStorage.getItem('authTokens')).access : null;\n      if (!token) {\n        const errorMessage = {\n          id: Date.now() + 1,\n          type: 'ai',\n          content: 'Bạn cần đăng nhập để sử dụng AI chatbox. Vui lòng đăng nhập và thử lại.',\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, errorMessage]);\n        setIsTyping(false);\n        setIsLoading(false);\n        return;\n      }\n      const requestData = {\n        message: message,\n        ...(sessionId && {\n          session_id: sessionId\n        }),\n        context: {}\n      };\n      console.log('Sending request data:', requestData);\n      const response = await fetch('http://localhost:8000/ai/chat/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `JWT ${token}`\n        },\n        body: JSON.stringify(requestData)\n      });\n      console.log('Response status:', response.status);\n      console.log('Response headers:', response.headers);\n      if (response.ok) {\n        const data = await response.json();\n        if (!sessionId) {\n          setSessionId(data.session_id);\n        }\n        const aiMessage = {\n          id: Date.now() + 1,\n          type: 'ai',\n          content: data.message,\n          timestamp: new Date(),\n          quickReplies: data.quick_replies || [],\n          suggestedProducts: data.suggested_products || [],\n          actionsTaken: data.actions_taken || []\n        };\n        setMessages(prev => [...prev, aiMessage]);\n      } else {\n        const errorText = await response.text();\n        console.error('Response error:', errorText);\n        throw new Error(`Server error: ${response.status} - ${errorText}`);\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n      let errorContent = 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại sau.';\n      if (error.message.includes('Failed to fetch')) {\n        errorContent = 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.';\n      } else if (error.message.includes('401')) {\n        errorContent = 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.';\n      } else if (error.message.includes('400')) {\n        errorContent = 'Dữ liệu không hợp lệ. Vui lòng thử lại.';\n      } else if (error.message.includes('500')) {\n        errorContent = 'Lỗi server. Vui lòng thử lại sau.';\n      }\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'ai',\n        content: errorContent,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsTyping(false);\n      setIsLoading(false);\n    }\n  };\n  const handleQuickReply = reply => {\n    sendMessage(reply);\n  };\n  const handleProductClick = product => {\n    // Mở sản phẩm trong tab mới\n    window.open(`/products/${product.id}`, '_blank');\n  };\n  const renderMessageContent = content => {\n    // Render message với markdown-style formatting\n    return content.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>') // **bold**\n    .replace(/👉 \\[([^\\]]+)\\]\\(([^)]+)\\)/g, '👉 <a href=\"$2\" target=\"_blank\" style=\"color: #007bff; text-decoration: none;\">$1</a>') // Links\n    .split('\\n').map((line, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      dangerouslySetInnerHTML: {\n        __html: line\n      }\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 9\n    }, this));\n  };\n  const formatTime = timestamp => {\n    return new Date(timestamp).toLocaleTimeString('vi-VN', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    size: \"lg\",\n    className: \"ai-chatbox-modal\",\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      className: \"ai-chatbox-header\",\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), \"Tr\\u1EE3 l\\xFD AI\", /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"success\",\n          className: \"ms-2\",\n          children: \"Online\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      className: \"ai-chatbox-body\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"messages-container\",\n        children: [messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `message ${message.type}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-avatar\",\n            children: message.type === 'ai' ? /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 42\n            }, this) : /*#__PURE__*/_jsxDEV(FaUser, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 56\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-bubble\",\n              children: message.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-time\",\n              children: formatTime(message.timestamp)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this), message.quickReplies && message.quickReplies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"quick-replies\",\n              children: message.quickReplies.map((reply, index) => /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-primary\",\n                size: \"sm\",\n                className: \"quick-reply-btn\",\n                onClick: () => handleQuickReply(reply),\n                children: reply\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 19\n            }, this), message.suggestedProducts && message.suggestedProducts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"suggested-products\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"S\\u1EA3n ph\\u1EA9m g\\u1EE3i \\xFD:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"products-grid\",\n                children: message.suggestedProducts.slice(0, 4).map(product => /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"product-card\",\n                  onClick: () => handleProductClick(product),\n                  children: [/*#__PURE__*/_jsxDEV(Card.Img, {\n                    variant: \"top\",\n                    src: product.image || '/placeholder.png',\n                    className: \"product-image\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"p-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Card.Title, {\n                      className: \"product-name\",\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Card.Text, {\n                      className: \"product-price\",\n                      children: [parseInt(product.price).toLocaleString('vi-VN'), \" VND\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 27\n                  }, this)]\n                }, product.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)]\n        }, message.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this)), isTyping && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"message ai\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-avatar\",\n            children: /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"typing-indicator\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      className: \"ai-chatbox-footer\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        className: \"message-form\",\n        onSubmit: e => {\n          e.preventDefault();\n          sendMessage();\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"input-group\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"text\",\n            placeholder: \"Nh\\u1EADp tin nh\\u1EAFn...\",\n            value: inputMessage,\n            onChange: e => setInputMessage(e.target.value),\n            disabled: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"primary\",\n            disabled: isLoading || !inputMessage.trim(),\n            children: isLoading ? /*#__PURE__*/_jsxDEV(Spinner, {\n              size: \"sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 28\n            }, this) : /*#__PURE__*/_jsxDEV(FaPaperPlane, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n};\n_s(AIChatbox, \"RDb7+g1HuNvQktWvGA27XmDMGhQ=\");\n_c = AIChatbox;\nexport default AIChatbox;\nvar _c;\n$RefreshReg$(_c, \"AIChatbox\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Modal", "<PERSON><PERSON>", "Form", "Card", "Badge", "Spinner", "FaRobot", "FaPaperPlane", "FaTimes", "FaUser", "jsxDEV", "_jsxDEV", "AIChatbox", "_ref", "_s", "show", "onHide", "userInfo", "messages", "setMessages", "inputMessage", "setInputMessage", "isTyping", "setIsTyping", "sessionId", "setSessionId", "isLoading", "setIsLoading", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "length", "handleWelcomeMessage", "welcomeMessage", "id", "Date", "now", "type", "content", "first_name", "timestamp", "quickReplies", "sendMessage", "message", "arguments", "undefined", "trim", "userMessage", "prev", "token", "localStorage", "getItem", "JSON", "parse", "access", "errorMessage", "requestData", "session_id", "context", "console", "log", "response", "fetch", "method", "headers", "body", "stringify", "status", "ok", "data", "json", "aiMessage", "quick_replies", "suggestedProducts", "suggested_products", "actionsTaken", "actions_taken", "errorText", "text", "error", "Error", "errorContent", "includes", "handleQuickReply", "reply", "handleProductClick", "product", "window", "open", "renderMessageContent", "replace", "split", "map", "line", "index", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatTime", "toLocaleTimeString", "hour", "minute", "size", "className", "children", "Header", "closeButton", "Title", "bg", "Body", "variant", "onClick", "slice", "Img", "src", "image", "name", "Text", "parseInt", "price", "toLocaleString", "ref", "Footer", "onSubmit", "e", "preventDefault", "Control", "placeholder", "value", "onChange", "target", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/AIChatbox.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Modal, Button, Form, Card, Badge, Spinner } from 'react-bootstrap';\nimport { FaRobot, FaPaperPlane, FaTimes, FaUser } from 'react-icons/fa';\nimport './AIChatbox.css';\n\nconst AIChatbox = ({ show, onHide, userInfo }) => {\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [sessionId, setSessionId] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    if (show && messages.length === 0) {\n      // G<PERSON>i tin nhắn chào mừng khi mở chatbox\n      handleWelcomeMessage();\n    }\n  }, [show]);\n\n  const handleWelcomeMessage = async () => {\n    const welcomeMessage = {\n      id: Date.now(),\n      type: 'ai',\n      content: `Xin chào${userInfo?.first_name ? ` ${userInfo.first_name}` : ''}! Tôi là trợ lý AI của shop. Tôi có thể giúp bạn tìm sản phẩm, chọn size, và hỗ trợ đặt hàng. Bạn cần hỗ trợ gì?`,\n      timestamp: new Date(),\n      quickReplies: ['Tìm sản phẩm', 'Hỗ trợ chọn size', 'Kiểm tra đơn hàng', 'Xem khuyến mãi']\n    };\n    setMessages([welcomeMessage]);\n  };\n\n  const sendMessage = async (message = inputMessage) => {\n    if (!message.trim()) return;\n\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: message,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsTyping(true);\n    setIsLoading(true);\n\n    try {\n      const token = localStorage.getItem('authTokens') ?\n        JSON.parse(localStorage.getItem('authTokens')).access : null;\n\n      if (!token) {\n        const errorMessage = {\n          id: Date.now() + 1,\n          type: 'ai',\n          content: 'Bạn cần đăng nhập để sử dụng AI chatbox. Vui lòng đăng nhập và thử lại.',\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, errorMessage]);\n        setIsTyping(false);\n        setIsLoading(false);\n        return;\n      }\n\n      const requestData = {\n        message: message,\n        ...(sessionId && { session_id: sessionId }),\n        context: {}\n      };\n\n      console.log('Sending request data:', requestData);\n\n      const response = await fetch('http://localhost:8000/ai/chat/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `JWT ${token}`\n        },\n        body: JSON.stringify(requestData)\n      });\n\n      console.log('Response status:', response.status);\n      console.log('Response headers:', response.headers);\n\n      if (response.ok) {\n        const data = await response.json();\n        \n        if (!sessionId) {\n          setSessionId(data.session_id);\n        }\n\n        const aiMessage = {\n          id: Date.now() + 1,\n          type: 'ai',\n          content: data.message,\n          timestamp: new Date(),\n          quickReplies: data.quick_replies || [],\n          suggestedProducts: data.suggested_products || [],\n          actionsTaken: data.actions_taken || []\n        };\n\n        setMessages(prev => [...prev, aiMessage]);\n      } else {\n        const errorText = await response.text();\n        console.error('Response error:', errorText);\n        throw new Error(`Server error: ${response.status} - ${errorText}`);\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n\n      let errorContent = 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại sau.';\n\n      if (error.message.includes('Failed to fetch')) {\n        errorContent = 'Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng.';\n      } else if (error.message.includes('401')) {\n        errorContent = 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.';\n      } else if (error.message.includes('400')) {\n        errorContent = 'Dữ liệu không hợp lệ. Vui lòng thử lại.';\n      } else if (error.message.includes('500')) {\n        errorContent = 'Lỗi server. Vui lòng thử lại sau.';\n      }\n\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'ai',\n        content: errorContent,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsTyping(false);\n      setIsLoading(false);\n    }\n  };\n\n  const handleQuickReply = (reply) => {\n    sendMessage(reply);\n  };\n\n  const handleProductClick = (product) => {\n    // Mở sản phẩm trong tab mới\n    window.open(`/products/${product.id}`, '_blank');\n  };\n\n  const renderMessageContent = (content) => {\n    // Render message với markdown-style formatting\n    return content\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>') // **bold**\n      .replace(/👉 \\[([^\\]]+)\\]\\(([^)]+)\\)/g, '👉 <a href=\"$2\" target=\"_blank\" style=\"color: #007bff; text-decoration: none;\">$1</a>') // Links\n      .split('\\n').map((line, index) => (\n        <div key={index} dangerouslySetInnerHTML={{ __html: line }} />\n      ));\n  };\n\n  const formatTime = (timestamp) => {\n    return new Date(timestamp).toLocaleTimeString('vi-VN', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <Modal show={show} onHide={onHide} size=\"lg\" className=\"ai-chatbox-modal\">\n      <Modal.Header closeButton className=\"ai-chatbox-header\">\n        <Modal.Title>\n          <FaRobot className=\"me-2\" />\n          Trợ lý AI\n          <Badge bg=\"success\" className=\"ms-2\">Online</Badge>\n        </Modal.Title>\n      </Modal.Header>\n      \n      <Modal.Body className=\"ai-chatbox-body\">\n        <div className=\"messages-container\">\n          {messages.map((message) => (\n            <div key={message.id} className={`message ${message.type}`}>\n              <div className=\"message-avatar\">\n                {message.type === 'ai' ? <FaRobot /> : <FaUser />}\n              </div>\n              \n              <div className=\"message-content\">\n                <div className=\"message-bubble\">\n                  {message.content}\n                </div>\n                \n                <div className=\"message-time\">\n                  {formatTime(message.timestamp)}\n                </div>\n\n                {/* Quick Replies */}\n                {message.quickReplies && message.quickReplies.length > 0 && (\n                  <div className=\"quick-replies\">\n                    {message.quickReplies.map((reply, index) => (\n                      <Button\n                        key={index}\n                        variant=\"outline-primary\"\n                        size=\"sm\"\n                        className=\"quick-reply-btn\"\n                        onClick={() => handleQuickReply(reply)}\n                      >\n                        {reply}\n                      </Button>\n                    ))}\n                  </div>\n                )}\n\n                {/* Suggested Products */}\n                {message.suggestedProducts && message.suggestedProducts.length > 0 && (\n                  <div className=\"suggested-products\">\n                    <h6>Sản phẩm gợi ý:</h6>\n                    <div className=\"products-grid\">\n                      {message.suggestedProducts.slice(0, 4).map((product) => (\n                        <Card \n                          key={product.id} \n                          className=\"product-card\"\n                          onClick={() => handleProductClick(product)}\n                        >\n                          <Card.Img \n                            variant=\"top\" \n                            src={product.image || '/placeholder.png'} \n                            className=\"product-image\"\n                          />\n                          <Card.Body className=\"p-2\">\n                            <Card.Title className=\"product-name\">\n                              {product.name}\n                            </Card.Title>\n                            <Card.Text className=\"product-price\">\n                              {parseInt(product.price).toLocaleString('vi-VN')} VND\n                            </Card.Text>\n                          </Card.Body>\n                        </Card>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n          \n          {isTyping && (\n            <div className=\"message ai\">\n              <div className=\"message-avatar\">\n                <FaRobot />\n              </div>\n              <div className=\"message-content\">\n                <div className=\"typing-indicator\">\n                  <span></span>\n                  <span></span>\n                  <span></span>\n                </div>\n              </div>\n            </div>\n          )}\n          \n          <div ref={messagesEndRef} />\n        </div>\n      </Modal.Body>\n      \n      <Modal.Footer className=\"ai-chatbox-footer\">\n        <Form className=\"message-form\" onSubmit={(e) => { e.preventDefault(); sendMessage(); }}>\n          <div className=\"input-group\">\n            <Form.Control\n              type=\"text\"\n              placeholder=\"Nhập tin nhắn...\"\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              disabled={isLoading}\n            />\n            <Button \n              type=\"submit\" \n              variant=\"primary\"\n              disabled={isLoading || !inputMessage.trim()}\n            >\n              {isLoading ? <Spinner size=\"sm\" /> : <FaPaperPlane />}\n            </Button>\n          </div>\n        </Form>\n      </Modal.Footer>\n    </Modal>\n  );\n};\n\nexport default AIChatbox;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AAC3E,SAASC,OAAO,EAAEC,YAAY,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AACvE,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGC,IAAA,IAAgC;EAAAC,EAAA;EAAA,IAA/B;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAAJ,IAAA;EAC3C,MAAM,CAACK,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM+B,cAAc,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAM8B,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDnC,SAAS,CAAC,MAAM;IACd+B,cAAc,EAAE;EAClB,CAAC,EAAE,CAACX,QAAQ,CAAC,CAAC;EAEdpB,SAAS,CAAC,MAAM;IACd,IAAIiB,IAAI,IAAIG,QAAQ,CAACgB,MAAM,KAAK,CAAC,EAAE;MACjC;MACAC,oBAAoB,EAAE;IACxB;EACF,CAAC,EAAE,CAACpB,IAAI,CAAC,CAAC;EAEV,MAAMoB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMC,cAAc,GAAG;MACrBC,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE;MACdC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAG,WAAUxB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEyB,UAAU,GAAI,IAAGzB,QAAQ,CAACyB,UAAW,EAAC,GAAG,EAAG,kHAAiH;MAC3LC,SAAS,EAAE,IAAIL,IAAI,EAAE;MACrBM,YAAY,EAAE,CAAC,cAAc,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,gBAAgB;IAC1F,CAAC;IACDzB,WAAW,CAAC,CAACiB,cAAc,CAAC,CAAC;EAC/B,CAAC;EAED,MAAMS,WAAW,GAAG,eAAAA,CAAA,EAAkC;IAAA,IAA3BC,OAAO,GAAAC,SAAA,CAAAb,MAAA,QAAAa,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG3B,YAAY;IAC/C,IAAI,CAAC0B,OAAO,CAACG,IAAI,EAAE,EAAE;IAErB,MAAMC,WAAW,GAAG;MAClBb,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE;MACdC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEK,OAAO;MAChBH,SAAS,EAAE,IAAIL,IAAI;IACrB,CAAC;IAEDnB,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,WAAW,CAAC,CAAC;IAC3C7B,eAAe,CAAC,EAAE,CAAC;IACnBE,WAAW,CAAC,IAAI,CAAC;IACjBI,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMyB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAC9CC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAACG,MAAM,GAAG,IAAI;MAE9D,IAAI,CAACL,KAAK,EAAE;QACV,MAAMM,YAAY,GAAG;UACnBrB,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC;UAClBC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE,yEAAyE;UAClFE,SAAS,EAAE,IAAIL,IAAI;QACrB,CAAC;QACDnB,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEO,YAAY,CAAC,CAAC;QAC5CnC,WAAW,CAAC,KAAK,CAAC;QAClBI,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;MAEA,MAAMgC,WAAW,GAAG;QAClBb,OAAO,EAAEA,OAAO;QAChB,IAAItB,SAAS,IAAI;UAAEoC,UAAU,EAAEpC;QAAU,CAAC,CAAC;QAC3CqC,OAAO,EAAE,CAAC;MACZ,CAAC;MAEDC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEJ,WAAW,CAAC;MAEjD,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgC,EAAE;QAC7DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,OAAMf,KAAM;QAChC,CAAC;QACDgB,IAAI,EAAEb,IAAI,CAACc,SAAS,CAACV,WAAW;MAClC,CAAC,CAAC;MAEFG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,QAAQ,CAACM,MAAM,CAAC;MAChDR,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,QAAQ,CAACG,OAAO,CAAC;MAElD,IAAIH,QAAQ,CAACO,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,EAAE;QAElC,IAAI,CAACjD,SAAS,EAAE;UACdC,YAAY,CAAC+C,IAAI,CAACZ,UAAU,CAAC;QAC/B;QAEA,MAAMc,SAAS,GAAG;UAChBrC,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC;UAClBC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE+B,IAAI,CAAC1B,OAAO;UACrBH,SAAS,EAAE,IAAIL,IAAI,EAAE;UACrBM,YAAY,EAAE4B,IAAI,CAACG,aAAa,IAAI,EAAE;UACtCC,iBAAiB,EAAEJ,IAAI,CAACK,kBAAkB,IAAI,EAAE;UAChDC,YAAY,EAAEN,IAAI,CAACO,aAAa,IAAI;QACtC,CAAC;QAED5D,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEuB,SAAS,CAAC,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMM,SAAS,GAAG,MAAMhB,QAAQ,CAACiB,IAAI,EAAE;QACvCnB,OAAO,CAACoB,KAAK,CAAC,iBAAiB,EAAEF,SAAS,CAAC;QAC3C,MAAM,IAAIG,KAAK,CAAE,iBAAgBnB,QAAQ,CAACM,MAAO,MAAKU,SAAU,EAAC,CAAC;MACpE;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAE9C,IAAIE,YAAY,GAAG,+CAA+C;MAElE,IAAIF,KAAK,CAACpC,OAAO,CAACuC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;QAC7CD,YAAY,GAAG,+DAA+D;MAChF,CAAC,MAAM,IAAIF,KAAK,CAACpC,OAAO,CAACuC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACxCD,YAAY,GAAG,qDAAqD;MACtE,CAAC,MAAM,IAAIF,KAAK,CAACpC,OAAO,CAACuC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACxCD,YAAY,GAAG,yCAAyC;MAC1D,CAAC,MAAM,IAAIF,KAAK,CAACpC,OAAO,CAACuC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACxCD,YAAY,GAAG,mCAAmC;MACpD;MAEA,MAAM1B,YAAY,GAAG;QACnBrB,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC;QAClBC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE2C,YAAY;QACrBzC,SAAS,EAAE,IAAIL,IAAI;MACrB,CAAC;MACDnB,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEO,YAAY,CAAC,CAAC;IAC9C,CAAC,SAAS;MACRnC,WAAW,CAAC,KAAK,CAAC;MAClBI,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM2D,gBAAgB,GAAIC,KAAK,IAAK;IAClC1C,WAAW,CAAC0C,KAAK,CAAC;EACpB,CAAC;EAED,MAAMC,kBAAkB,GAAIC,OAAO,IAAK;IACtC;IACAC,MAAM,CAACC,IAAI,CAAE,aAAYF,OAAO,CAACpD,EAAG,EAAC,EAAE,QAAQ,CAAC;EAClD,CAAC;EAED,MAAMuD,oBAAoB,GAAInD,OAAO,IAAK;IACxC;IACA,OAAOA,OAAO,CACXoD,OAAO,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAAC;IAAA,CACjDA,OAAO,CAAC,6BAA6B,EAAE,uFAAuF,CAAC,CAAC;IAAA,CAChIC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3BtF,OAAA;MAAiBuF,uBAAuB,EAAE;QAAEC,MAAM,EAAEH;MAAK;IAAE,GAAjDC,KAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAChB,CAAC;EACN,CAAC;EAED,MAAMC,UAAU,GAAI7D,SAAS,IAAK;IAChC,OAAO,IAAIL,IAAI,CAACK,SAAS,CAAC,CAAC8D,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACEhG,OAAA,CAACX,KAAK;IAACe,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAAC4F,IAAI,EAAC,IAAI;IAACC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBACvEnG,OAAA,CAACX,KAAK,CAAC+G,MAAM;MAACC,WAAW;MAACH,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eACrDnG,OAAA,CAACX,KAAK,CAACiH,KAAK;QAAAH,QAAA,gBACVnG,OAAA,CAACL,OAAO;UAACuG,SAAS,EAAC;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG,qBAE5B,eAAA5F,OAAA,CAACP,KAAK;UAAC8G,EAAE,EAAC,SAAS;UAACL,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAM;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAQ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACvC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACD,eAEf5F,OAAA,CAACX,KAAK,CAACmH,IAAI;MAACN,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eACrCnG,OAAA;QAAKkG,SAAS,EAAC,oBAAoB;QAAAC,QAAA,GAChC5F,QAAQ,CAAC6E,GAAG,CAAEjD,OAAO,iBACpBnC,OAAA;UAAsBkG,SAAS,EAAG,WAAU/D,OAAO,CAACN,IAAK,EAAE;UAAAsE,QAAA,gBACzDnG,OAAA;YAAKkG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BhE,OAAO,CAACN,IAAI,KAAK,IAAI,gBAAG7B,OAAA,CAACL,OAAO;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAG,gBAAG5F,OAAA,CAACF,MAAM;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAC7C,eAEN5F,OAAA;YAAKkG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BnG,OAAA;cAAKkG,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5BhE,OAAO,CAACL;YAAO;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACZ,eAEN5F,OAAA;cAAKkG,SAAS,EAAC,cAAc;cAAAC,QAAA,EAC1BN,UAAU,CAAC1D,OAAO,CAACH,SAAS;YAAC;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAC1B,EAGLzD,OAAO,CAACF,YAAY,IAAIE,OAAO,CAACF,YAAY,CAACV,MAAM,GAAG,CAAC,iBACtDvB,OAAA;cAAKkG,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3BhE,OAAO,CAACF,YAAY,CAACmD,GAAG,CAAC,CAACR,KAAK,EAAEU,KAAK,kBACrCtF,OAAA,CAACV,MAAM;gBAELmH,OAAO,EAAC,iBAAiB;gBACzBR,IAAI,EAAC,IAAI;gBACTC,SAAS,EAAC,iBAAiB;gBAC3BQ,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAACC,KAAK,CAAE;gBAAAuB,QAAA,EAEtCvB;cAAK,GANDU,KAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAQb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAEL,EAGAzD,OAAO,CAAC8B,iBAAiB,IAAI9B,OAAO,CAAC8B,iBAAiB,CAAC1C,MAAM,GAAG,CAAC,iBAChEvB,OAAA;cAAKkG,SAAS,EAAC,oBAAoB;cAAAC,QAAA,gBACjCnG,OAAA;gBAAAmG,QAAA,EAAI;cAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAK,eACxB5F,OAAA;gBAAKkG,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3BhE,OAAO,CAAC8B,iBAAiB,CAAC0C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACvB,GAAG,CAAEN,OAAO,iBACjD9E,OAAA,CAACR,IAAI;kBAEH0G,SAAS,EAAC,cAAc;kBACxBQ,OAAO,EAAEA,CAAA,KAAM7B,kBAAkB,CAACC,OAAO,CAAE;kBAAAqB,QAAA,gBAE3CnG,OAAA,CAACR,IAAI,CAACoH,GAAG;oBACPH,OAAO,EAAC,KAAK;oBACbI,GAAG,EAAE/B,OAAO,CAACgC,KAAK,IAAI,kBAAmB;oBACzCZ,SAAS,EAAC;kBAAe;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACzB,eACF5F,OAAA,CAACR,IAAI,CAACgH,IAAI;oBAACN,SAAS,EAAC,KAAK;oBAAAC,QAAA,gBACxBnG,OAAA,CAACR,IAAI,CAAC8G,KAAK;sBAACJ,SAAS,EAAC,cAAc;sBAAAC,QAAA,EACjCrB,OAAO,CAACiC;oBAAI;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QACF,eACb5F,OAAA,CAACR,IAAI,CAACwH,IAAI;sBAACd,SAAS,EAAC,eAAe;sBAAAC,QAAA,GACjCc,QAAQ,CAACnC,OAAO,CAACoC,KAAK,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC,EAAC,MACnD;oBAAA;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,QAAY;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACF;gBAAA,GAhBPd,OAAO,CAACpD,EAAE;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAkBlB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAET;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACG;QAAA,GA5DEzD,OAAO,CAACT,EAAE;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QA8DrB,CAAC,EAEDjF,QAAQ,iBACPX,OAAA;UAAKkG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBnG,OAAA;YAAKkG,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BnG,OAAA,CAACL,OAAO;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACP,eACN5F,OAAA;YAAKkG,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BnG,OAAA;cAAKkG,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BnG,OAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAa,eACb5F,OAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAa,eACb5F,OAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAa;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAET,eAED5F,OAAA;UAAKoH,GAAG,EAAEnG;QAAe;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACxB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACK,eAEb5F,OAAA,CAACX,KAAK,CAACgI,MAAM;MAACnB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eACzCnG,OAAA,CAACT,IAAI;QAAC2G,SAAS,EAAC,cAAc;QAACoB,QAAQ,EAAGC,CAAC,IAAK;UAAEA,CAAC,CAACC,cAAc,EAAE;UAAEtF,WAAW,EAAE;QAAE,CAAE;QAAAiE,QAAA,eACrFnG,OAAA;UAAKkG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnG,OAAA,CAACT,IAAI,CAACkI,OAAO;YACX5F,IAAI,EAAC,MAAM;YACX6F,WAAW,EAAC,4BAAkB;YAC9BC,KAAK,EAAElH,YAAa;YACpBmH,QAAQ,EAAGL,CAAC,IAAK7G,eAAe,CAAC6G,CAAC,CAACM,MAAM,CAACF,KAAK,CAAE;YACjDG,QAAQ,EAAE/G;UAAU;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACpB,eACF5F,OAAA,CAACV,MAAM;YACLuC,IAAI,EAAC,QAAQ;YACb4E,OAAO,EAAC,SAAS;YACjBqB,QAAQ,EAAE/G,SAAS,IAAI,CAACN,YAAY,CAAC6B,IAAI,EAAG;YAAA6D,QAAA,EAE3CpF,SAAS,gBAAGf,OAAA,CAACN,OAAO;cAACuG,IAAI,EAAC;YAAI;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAG,gBAAG5F,OAAA,CAACJ,YAAY;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAC9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACM;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACT;AAEZ,CAAC;AAACzF,EAAA,CAzRIF,SAAS;AAAA8H,EAAA,GAAT9H,SAAS;AA2Rf,eAAeA,SAAS;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}