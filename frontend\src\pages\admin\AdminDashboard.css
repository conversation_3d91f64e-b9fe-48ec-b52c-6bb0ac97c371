.admin-dashboard {
  padding: 0;
}

/* Stat Cards */
.stat-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.stat-card-orange {
  background: linear-gradient(135deg, #ff9800, #ff5722);
  color: white;
}

.stat-card-blue {
  background: linear-gradient(135deg, #2196f3, #03a9f4);
  color: white;
}

.stat-card-green {
  background: linear-gradient(135deg, #4caf50, #8bc34a);
  color: white;
}

.stat-card-pink {
  background: linear-gradient(135deg, #e91e63, #f06292);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.stat-info h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
}

.stat-info p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Social Media Cards */
.social-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  color: white;
}

.social-card:hover {
  transform: translateY(-2px);
}

.facebook-card {
  background: linear-gradient(135deg, #3b5998, #4267b2);
}

.twitter-card {
  background: linear-gradient(135deg, #1da1f2, #0d8bd9);
}

.linkedin-card {
  background: linear-gradient(135deg, #0077b5, #005885);
}

.google-card {
  background: linear-gradient(135deg, #dd4b39, #c23321);
}

.social-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.social-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.social-stats {
  flex: 1;
}

.social-numbers {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.big-number {
  font-size: 1.5rem;
  font-weight: 700;
}

.small-number {
  font-size: 1.2rem;
  font-weight: 600;
}

.social-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  opacity: 0.9;
}

/* Chart Card */
.chart-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chart-card .card-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 20px;
}

.chart-card .card-header h5 {
  margin: 0;
  color: #333;
  font-weight: 600;
}

.chart-placeholder {
  padding: 20px;
}

.chart-legend {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #666;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.chart-area {
  height: 300px;
  background: #f8f9fa;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
}

.chart-area p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.chart-area small {
  margin-top: 5px;
  opacity: 0.7;
}

/* Recent Activity */
.recent-activity {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item i {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(102, 126, 234, 0.1);
}

.activity-content {
  flex: 1;
}

.activity-content p {
  margin: 0;
  font-size: 0.9rem;
}

.activity-content small {
  color: #6c757d;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.quick-actions .btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.quick-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive */
@media (max-width: 768px) {
  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .quick-actions {
    justify-content: center;
  }

  .quick-actions .btn {
    flex: 1;
    min-width: 120px;
  }
}
