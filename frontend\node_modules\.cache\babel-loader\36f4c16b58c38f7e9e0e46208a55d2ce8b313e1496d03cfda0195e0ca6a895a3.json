{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\pages\\\\AIChatTestPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Row, Col, Button } from 'react-bootstrap';\nimport AIChatTester from '../components/AIChatTester';\nimport AIChatbox from '../components/AIChatbox';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AIChatTestPage = () => {\n  _s();\n  const [showChatbox, setShowChatbox] = useState(false);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"py-4\",\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83E\\uDD16 AI Chat Test Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Use this page to test AI Chat functionality before using the main chatbox\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(AIChatTester, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sticky-top\",\n          style: {\n            top: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            children: \"Live Chatbox Test\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"small text-muted\",\n            children: \"Test the actual chatbox component here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: () => setShowChatbox(true),\n            className: \"mb-3\",\n            children: \"Open AI Chatbox\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AIChatbox, {\n            show: showChatbox,\n            onHide: () => setShowChatbox(false),\n            userInfo: {\n              first_name: 'Test User'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Quick Links:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-grid gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-secondary\",\n                size: \"sm\",\n                href: \"/login\",\n                children: \"Login Page\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-secondary\",\n                size: \"sm\",\n                href: \"/\",\n                children: \"Home Page\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-secondary\",\n                size: \"sm\",\n                href: \"/admin\",\n                children: \"Admin Panel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_s(AIChatTestPage, \"Cxfdx49n2upKmJ3W9wKLYshebCg=\");\n_c = AIChatTestPage;\nexport default AIChatTestPage;\nvar _c;\n$RefreshReg$(_c, \"AIChatTestPage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "<PERSON><PERSON>", "AIChatTester", "AIChatbox", "jsxDEV", "_jsxDEV", "AIChatTestPage", "_s", "showChatbox", "setShowChatbox", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "lg", "style", "top", "variant", "onClick", "show", "onHide", "userInfo", "first_name", "size", "href", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/pages/AIChatTestPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Container, Row, Col, Button } from 'react-bootstrap';\nimport AIChatTester from '../components/AIChatTester';\nimport AIChatbox from '../components/AIChatbox';\n\nconst AIChatTestPage = () => {\n  const [showChatbox, setShowChatbox] = useState(false);\n\n  return (\n    <Container className=\"py-4\">\n      <Row>\n        <Col>\n          <h2>🤖 AI Chat Test Page</h2>\n          <p>Use this page to test AI Chat functionality before using the main chatbox</p>\n        </Col>\n      </Row>\n\n      <Row>\n        <Col lg={8}>\n          <AIChatTester />\n        </Col>\n        <Col lg={4}>\n          <div className=\"sticky-top\" style={{top: '20px'}}>\n            <h5>Live Chatbox Test</h5>\n            <p className=\"small text-muted\">\n              Test the actual chatbox component here\n            </p>\n            <Button\n              variant=\"primary\"\n              onClick={() => setShowChatbox(true)}\n              className=\"mb-3\"\n            >\n              Open AI Chatbox\n            </Button>\n\n            <AIChatbox\n              show={showChatbox}\n              onHide={() => setShowChatbox(false)}\n              userInfo={{first_name: 'Test User'}}\n            />\n\n            <div className=\"mt-3\">\n              <h6>Quick Links:</h6>\n              <div className=\"d-grid gap-2\">\n                <Button variant=\"outline-secondary\" size=\"sm\" href=\"/login\">\n                  Login Page\n                </Button>\n                <Button variant=\"outline-secondary\" size=\"sm\" href=\"/\">\n                  Home Page\n                </Button>\n                <Button variant=\"outline-secondary\" size=\"sm\" href=\"/admin\">\n                  Admin Panel\n                </Button>\n              </div>\n            </div>\n          </div>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default AIChatTestPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,QAAQ,iBAAiB;AAC7D,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,SAAS,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAErD,oBACEQ,OAAA,CAACP,SAAS;IAACY,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACzBN,OAAA,CAACN,GAAG;MAAAY,QAAA,eACFN,OAAA,CAACL,GAAG;QAAAW,QAAA,gBACFN,OAAA;UAAAM,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK,eAC7BV,OAAA;UAAAM,QAAA,EAAG;QAAyE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAI;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAC5E;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF,eAENV,OAAA,CAACN,GAAG;MAAAY,QAAA,gBACFN,OAAA,CAACL,GAAG;QAACgB,EAAE,EAAE,CAAE;QAAAL,QAAA,eACTN,OAAA,CAACH,YAAY;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACZ,eACNV,OAAA,CAACL,GAAG;QAACgB,EAAE,EAAE,CAAE;QAAAL,QAAA,eACTN,OAAA;UAAKK,SAAS,EAAC,YAAY;UAACO,KAAK,EAAE;YAACC,GAAG,EAAE;UAAM,CAAE;UAAAP,QAAA,gBAC/CN,OAAA;YAAAM,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eAC1BV,OAAA;YAAGK,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAEhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAI,eACJV,OAAA,CAACJ,MAAM;YACLkB,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAEA,CAAA,KAAMX,cAAc,CAAC,IAAI,CAAE;YACpCC,SAAS,EAAC,MAAM;YAAAC,QAAA,EACjB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAS,eAETV,OAAA,CAACF,SAAS;YACRkB,IAAI,EAAEb,WAAY;YAClBc,MAAM,EAAEA,CAAA,KAAMb,cAAc,CAAC,KAAK,CAAE;YACpCc,QAAQ,EAAE;cAACC,UAAU,EAAE;YAAW;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACpC,eAEFV,OAAA;YAAKK,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBN,OAAA;cAAAM,QAAA,EAAI;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QAAK,eACrBV,OAAA;cAAKK,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BN,OAAA,CAACJ,MAAM;gBAACkB,OAAO,EAAC,mBAAmB;gBAACM,IAAI,EAAC,IAAI;gBAACC,IAAI,EAAC,QAAQ;gBAAAf,QAAA,EAAC;cAE5D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAS,eACTV,OAAA,CAACJ,MAAM;gBAACkB,OAAO,EAAC,mBAAmB;gBAACM,IAAI,EAAC,IAAI;gBAACC,IAAI,EAAC,GAAG;gBAAAf,QAAA,EAAC;cAEvD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAS,eACTV,OAAA,CAACJ,MAAM;gBAACkB,OAAO,EAAC,mBAAmB;gBAACM,IAAI,EAAC,IAAI;gBAACC,IAAI,EAAC,QAAQ;gBAAAf,QAAA,EAAC;cAE5D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAS;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,QACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACI;AAEhB,CAAC;AAACR,EAAA,CAvDID,cAAc;AAAAqB,EAAA,GAAdrB,cAAc;AAyDpB,eAAeA,cAAc;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}