{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\AIChatTester.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Button, Form, Alert, Badge, ListGroup } from 'react-bootstrap';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AIChatTester = () => {\n  _s();\n  const [testMessage, setTestMessage] = useState('');\n  const [responses, setResponses] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState('unknown');\n  const testMessages = ['xin chào', 'tìm áo màu xanh', 'tìm giày dưới 500k', 'quần jean màu đen', 'sản phẩm giá rẻ', 'áo thun trắng', 'size L có vừa không?', 'có khuyến mãi nào không?', 'hướng dẫn đặt hàng'];\n  const addResponse = function (type, message) {\n    let data = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    let error = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n    const response = {\n      id: Date.now(),\n      type,\n      message,\n      data,\n      error,\n      timestamp: new Date().toLocaleTimeString()\n    };\n    setResponses(prev => [response, ...prev]);\n  };\n  const testConnection = async () => {\n    setIsLoading(true);\n    try {\n      // Test backend connection\n      const response = await fetch('http://localhost:8000/ai/test/');\n      if (response.ok) {\n        const data = await response.json();\n        setConnectionStatus('connected');\n        addResponse('success', 'Backend connection successful', data);\n      } else {\n        setConnectionStatus('error');\n        addResponse('error', `Backend connection failed: ${response.status}`);\n      }\n    } catch (error) {\n      setConnectionStatus('error');\n      addResponse('error', 'Cannot connect to backend', null, error.message);\n    }\n    setIsLoading(false);\n  };\n  const testAIChat = async message => {\n    setIsLoading(true);\n    try {\n      const token = localStorage.getItem('authTokens') ? JSON.parse(localStorage.getItem('authTokens')).access : null;\n      if (!token) {\n        addResponse('warning', 'No auth token found. Please login first.');\n        setIsLoading(false);\n        return;\n      }\n      const requestData = {\n        message: message,\n        context: {}\n      };\n      addResponse('info', `Sending: \"${message}\"`);\n      const response = await fetch('http://localhost:8000/ai/chat/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `JWT ${token}`\n        },\n        body: JSON.stringify(requestData)\n      });\n      if (response.ok) {\n        const data = await response.json();\n        addResponse('success', `AI Response: \"${data.message}\"`, data);\n      } else {\n        const errorText = await response.text();\n        addResponse('error', `AI Chat failed: ${response.status}`, null, errorText);\n      }\n    } catch (error) {\n      addResponse('error', 'AI Chat request failed', null, error.message);\n    }\n    setIsLoading(false);\n  };\n  const testAllMessages = async () => {\n    for (const message of testMessages) {\n      await testAIChat(message);\n      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests\n    }\n  };\n\n  const clearResponses = () => {\n    setResponses([]);\n  };\n  const getStatusColor = type => {\n    switch (type) {\n      case 'success':\n        return 'success';\n      case 'warning':\n        return 'warning';\n      case 'error':\n        return 'danger';\n      case 'info':\n        return 'info';\n      default:\n        return 'secondary';\n    }\n  };\n  const getConnectionBadge = () => {\n    switch (connectionStatus) {\n      case 'connected':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"success\",\n          children: \"Connected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"danger\",\n          children: \"Disconnected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"secondary\",\n          children: \"Unknown\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          children: \"\\uD83E\\uDDEA AI Chat Tester\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), getConnectionBadge()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-primary\",\n          onClick: testConnection,\n          disabled: isLoading,\n          className: \"me-2\",\n          children: \"Test Connection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-success\",\n          onClick: testAllMessages,\n          disabled: isLoading,\n          className: \"me-2\",\n          children: \"Test All Messages\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-secondary\",\n          onClick: clearResponses,\n          disabled: isLoading,\n          children: \"Clear\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"text\",\n            placeholder: \"Enter test message...\",\n            value: testMessage,\n            onChange: e => setTestMessage(e.target.value),\n            onKeyPress: e => e.key === 'Enter' && testAIChat(testMessage)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: () => testAIChat(testMessage),\n            disabled: isLoading || !testMessage.trim(),\n            className: \"ms-2\",\n            children: \"Test\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: \"Quick Tests:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-wrap gap-2\",\n          children: testMessages.map((msg, index) => /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline-info\",\n            size: \"sm\",\n            onClick: () => testAIChat(msg),\n            disabled: isLoading,\n            children: msg\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: \"Authentication Status:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), localStorage.getItem('authTokens') ? /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"success\",\n          className: \"py-2\",\n          children: \"\\u2705 Auth token found in localStorage\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"warning\",\n          className: \"py-2\",\n          children: [\"\\u26A0\\uFE0F No auth token found. Please \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/login\",\n            children: \"login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 46\n          }, this), \" first.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), responses.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: \"Test Results:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListGroup, {\n          style: {\n            maxHeight: '400px',\n            overflowY: 'auto'\n          },\n          children: responses.map(response => /*#__PURE__*/_jsxDEV(ListGroup.Item, {\n            variant: getStatusColor(response.type),\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-grow-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: response.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 23\n                }, this), response.data && /*#__PURE__*/_jsxDEV(\"details\", {\n                  className: \"mt-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n                    children: \"Response Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                    className: \"mt-1\",\n                    style: {\n                      fontSize: '0.8rem',\n                      maxHeight: '200px',\n                      overflow: 'auto'\n                    },\n                    children: JSON.stringify(response.data, null, 2)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 25\n                }, this), response.error && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-1 text-danger\",\n                  style: {\n                    fontSize: '0.9rem'\n                  },\n                  children: [\"Error: \", response.error]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted ms-2\",\n                children: response.timestamp\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 19\n            }, this)\n          }, response.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n          children: \"Instructions:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n          className: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Make sure Django server is running on port 8000\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Login to get authentication token\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Click \\\"Test Connection\\\" to verify backend\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Use quick test buttons or enter custom messages\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Check responses for errors or success\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_s(AIChatTester, \"gBRg3Af6rDVIQH1ztcfUj6guJx4=\");\n_c = AIChatTester;\nexport default AIChatTester;\nvar _c;\n$RefreshReg$(_c, \"AIChatTester\");", "map": {"version": 3, "names": ["React", "useState", "Card", "<PERSON><PERSON>", "Form", "<PERSON><PERSON>", "Badge", "ListGroup", "jsxDEV", "_jsxDEV", "AIChatTester", "_s", "testMessage", "setTestMessage", "responses", "setResponses", "isLoading", "setIsLoading", "connectionStatus", "setConnectionStatus", "testMessages", "addResponse", "type", "message", "data", "arguments", "length", "undefined", "error", "response", "id", "Date", "now", "timestamp", "toLocaleTimeString", "prev", "testConnection", "fetch", "ok", "json", "status", "testAIChat", "token", "localStorage", "getItem", "JSON", "parse", "access", "requestData", "context", "method", "headers", "body", "stringify", "errorText", "text", "testAllMessages", "Promise", "resolve", "setTimeout", "clearResponses", "getStatusColor", "getConnectionBadge", "bg", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Header", "className", "Body", "variant", "onClick", "disabled", "Control", "placeholder", "value", "onChange", "e", "target", "onKeyPress", "key", "trim", "map", "msg", "index", "size", "href", "style", "maxHeight", "overflowY", "<PERSON><PERSON>", "fontSize", "overflow", "_c", "$RefreshReg$"], "sources": ["D:/Dong_A/Nam III/KienTap/Python-KienTap-/frontend/src/components/AIChatTester.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Card, Button, Form, Alert, Badge, ListGroup } from 'react-bootstrap';\n\nconst AIChatTester = () => {\n  const [testMessage, setTestMessage] = useState('');\n  const [responses, setResponses] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [connectionStatus, setConnectionStatus] = useState('unknown');\n\n  const testMessages = [\n    'xin chào',\n    'tìm áo màu xanh',\n    'tìm giày dưới 500k',\n    'quần jean màu đen',\n    'sản phẩm giá rẻ',\n    'áo thun trắng',\n    'size L có vừa không?',\n    'có khuyến mãi nào không?',\n    'hướng dẫn đặt hàng'\n  ];\n\n  const addResponse = (type, message, data = null, error = null) => {\n    const response = {\n      id: Date.now(),\n      type,\n      message,\n      data,\n      error,\n      timestamp: new Date().toLocaleTimeString()\n    };\n    setResponses(prev => [response, ...prev]);\n  };\n\n  const testConnection = async () => {\n    setIsLoading(true);\n    try {\n      // Test backend connection\n      const response = await fetch('http://localhost:8000/ai/test/');\n      if (response.ok) {\n        const data = await response.json();\n        setConnectionStatus('connected');\n        addResponse('success', 'Backend connection successful', data);\n      } else {\n        setConnectionStatus('error');\n        addResponse('error', `Backend connection failed: ${response.status}`);\n      }\n    } catch (error) {\n      setConnectionStatus('error');\n      addResponse('error', 'Cannot connect to backend', null, error.message);\n    }\n    setIsLoading(false);\n  };\n\n  const testAIChat = async (message) => {\n    setIsLoading(true);\n    try {\n      const token = localStorage.getItem('authTokens') ? \n        JSON.parse(localStorage.getItem('authTokens')).access : null;\n\n      if (!token) {\n        addResponse('warning', 'No auth token found. Please login first.');\n        setIsLoading(false);\n        return;\n      }\n\n      const requestData = {\n        message: message,\n        context: {}\n      };\n\n      addResponse('info', `Sending: \"${message}\"`);\n\n      const response = await fetch('http://localhost:8000/ai/chat/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `JWT ${token}`\n        },\n        body: JSON.stringify(requestData)\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        addResponse('success', `AI Response: \"${data.message}\"`, data);\n      } else {\n        const errorText = await response.text();\n        addResponse('error', `AI Chat failed: ${response.status}`, null, errorText);\n      }\n    } catch (error) {\n      addResponse('error', 'AI Chat request failed', null, error.message);\n    }\n    setIsLoading(false);\n  };\n\n  const testAllMessages = async () => {\n    for (const message of testMessages) {\n      await testAIChat(message);\n      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests\n    }\n  };\n\n  const clearResponses = () => {\n    setResponses([]);\n  };\n\n  const getStatusColor = (type) => {\n    switch (type) {\n      case 'success': return 'success';\n      case 'warning': return 'warning';\n      case 'error': return 'danger';\n      case 'info': return 'info';\n      default: return 'secondary';\n    }\n  };\n\n  const getConnectionBadge = () => {\n    switch (connectionStatus) {\n      case 'connected':\n        return <Badge bg=\"success\">Connected</Badge>;\n      case 'error':\n        return <Badge bg=\"danger\">Disconnected</Badge>;\n      default:\n        return <Badge bg=\"secondary\">Unknown</Badge>;\n    }\n  };\n\n  return (\n    <Card>\n      <Card.Header>\n        <div className=\"d-flex justify-content-between align-items-center\">\n          <h5>🧪 AI Chat Tester</h5>\n          {getConnectionBadge()}\n        </div>\n      </Card.Header>\n      <Card.Body>\n        {/* Connection Test */}\n        <div className=\"mb-3\">\n          <Button \n            variant=\"outline-primary\" \n            onClick={testConnection}\n            disabled={isLoading}\n            className=\"me-2\"\n          >\n            Test Connection\n          </Button>\n          <Button \n            variant=\"outline-success\" \n            onClick={testAllMessages}\n            disabled={isLoading}\n            className=\"me-2\"\n          >\n            Test All Messages\n          </Button>\n          <Button \n            variant=\"outline-secondary\" \n            onClick={clearResponses}\n            disabled={isLoading}\n          >\n            Clear\n          </Button>\n        </div>\n\n        {/* Custom Message Test */}\n        <Form className=\"mb-3\">\n          <div className=\"d-flex\">\n            <Form.Control\n              type=\"text\"\n              placeholder=\"Enter test message...\"\n              value={testMessage}\n              onChange={(e) => setTestMessage(e.target.value)}\n              onKeyPress={(e) => e.key === 'Enter' && testAIChat(testMessage)}\n            />\n            <Button \n              variant=\"primary\" \n              onClick={() => testAIChat(testMessage)}\n              disabled={isLoading || !testMessage.trim()}\n              className=\"ms-2\"\n            >\n              Test\n            </Button>\n          </div>\n        </Form>\n\n        {/* Quick Test Buttons */}\n        <div className=\"mb-3\">\n          <h6>Quick Tests:</h6>\n          <div className=\"d-flex flex-wrap gap-2\">\n            {testMessages.map((msg, index) => (\n              <Button\n                key={index}\n                variant=\"outline-info\"\n                size=\"sm\"\n                onClick={() => testAIChat(msg)}\n                disabled={isLoading}\n              >\n                {msg}\n              </Button>\n            ))}\n          </div>\n        </div>\n\n        {/* Auth Status */}\n        <div className=\"mb-3\">\n          <h6>Authentication Status:</h6>\n          {localStorage.getItem('authTokens') ? (\n            <Alert variant=\"success\" className=\"py-2\">\n              ✅ Auth token found in localStorage\n            </Alert>\n          ) : (\n            <Alert variant=\"warning\" className=\"py-2\">\n              ⚠️ No auth token found. Please <a href=\"/login\">login</a> first.\n            </Alert>\n          )}\n        </div>\n\n        {/* Responses */}\n        {responses.length > 0 && (\n          <div>\n            <h6>Test Results:</h6>\n            <ListGroup style={{ maxHeight: '400px', overflowY: 'auto' }}>\n              {responses.map((response) => (\n                <ListGroup.Item \n                  key={response.id}\n                  variant={getStatusColor(response.type)}\n                >\n                  <div className=\"d-flex justify-content-between align-items-start\">\n                    <div className=\"flex-grow-1\">\n                      <strong>{response.message}</strong>\n                      {response.data && (\n                        <details className=\"mt-2\">\n                          <summary>Response Data</summary>\n                          <pre className=\"mt-1\" style={{fontSize: '0.8rem', maxHeight: '200px', overflow: 'auto'}}>\n                            {JSON.stringify(response.data, null, 2)}\n                          </pre>\n                        </details>\n                      )}\n                      {response.error && (\n                        <div className=\"mt-1 text-danger\" style={{fontSize: '0.9rem'}}>\n                          Error: {response.error}\n                        </div>\n                      )}\n                    </div>\n                    <small className=\"text-muted ms-2\">{response.timestamp}</small>\n                  </div>\n                </ListGroup.Item>\n              ))}\n            </ListGroup>\n          </div>\n        )}\n\n        {/* Instructions */}\n        <div className=\"mt-3\">\n          <h6>Instructions:</h6>\n          <ol className=\"small\">\n            <li>Make sure Django server is running on port 8000</li>\n            <li>Login to get authentication token</li>\n            <li>Click \"Test Connection\" to verify backend</li>\n            <li>Use quick test buttons or enter custom messages</li>\n            <li>Check responses for errors or success</li>\n          </ol>\n        </div>\n      </Card.Body>\n    </Card>\n  );\n};\n\nexport default AIChatTester;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC,SAAS,CAAC;EAEnE,MAAMmB,YAAY,GAAG,CACnB,UAAU,EACV,iBAAiB,EACjB,oBAAoB,EACpB,mBAAmB,EACnB,iBAAiB,EACjB,eAAe,EACf,sBAAsB,EACtB,0BAA0B,EAC1B,oBAAoB,CACrB;EAED,MAAMC,WAAW,GAAG,SAAAA,CAACC,IAAI,EAAEC,OAAO,EAAgC;IAAA,IAA9BC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAAA,IAAEG,KAAK,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAC3D,MAAMI,QAAQ,GAAG;MACfC,EAAE,EAAEC,IAAI,CAACC,GAAG,EAAE;MACdV,IAAI;MACJC,OAAO;MACPC,IAAI;MACJI,KAAK;MACLK,SAAS,EAAE,IAAIF,IAAI,EAAE,CAACG,kBAAkB;IAC1C,CAAC;IACDnB,YAAY,CAACoB,IAAI,IAAI,CAACN,QAAQ,EAAE,GAAGM,IAAI,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCnB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF;MACA,MAAMY,QAAQ,GAAG,MAAMQ,KAAK,CAAC,gCAAgC,CAAC;MAC9D,IAAIR,QAAQ,CAACS,EAAE,EAAE;QACf,MAAMd,IAAI,GAAG,MAAMK,QAAQ,CAACU,IAAI,EAAE;QAClCpB,mBAAmB,CAAC,WAAW,CAAC;QAChCE,WAAW,CAAC,SAAS,EAAE,+BAA+B,EAAEG,IAAI,CAAC;MAC/D,CAAC,MAAM;QACLL,mBAAmB,CAAC,OAAO,CAAC;QAC5BE,WAAW,CAAC,OAAO,EAAG,8BAA6BQ,QAAQ,CAACW,MAAO,EAAC,CAAC;MACvE;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdT,mBAAmB,CAAC,OAAO,CAAC;MAC5BE,WAAW,CAAC,OAAO,EAAE,2BAA2B,EAAE,IAAI,EAAEO,KAAK,CAACL,OAAO,CAAC;IACxE;IACAN,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMwB,UAAU,GAAG,MAAOlB,OAAO,IAAK;IACpCN,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMyB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAC9CC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC,CAACG,MAAM,GAAG,IAAI;MAE9D,IAAI,CAACL,KAAK,EAAE;QACVrB,WAAW,CAAC,SAAS,EAAE,0CAA0C,CAAC;QAClEJ,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;MAEA,MAAM+B,WAAW,GAAG;QAClBzB,OAAO,EAAEA,OAAO;QAChB0B,OAAO,EAAE,CAAC;MACZ,CAAC;MAED5B,WAAW,CAAC,MAAM,EAAG,aAAYE,OAAQ,GAAE,CAAC;MAE5C,MAAMM,QAAQ,GAAG,MAAMQ,KAAK,CAAC,gCAAgC,EAAE;QAC7Da,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,OAAMT,KAAM;QAChC,CAAC;QACDU,IAAI,EAAEP,IAAI,CAACQ,SAAS,CAACL,WAAW;MAClC,CAAC,CAAC;MAEF,IAAInB,QAAQ,CAACS,EAAE,EAAE;QACf,MAAMd,IAAI,GAAG,MAAMK,QAAQ,CAACU,IAAI,EAAE;QAClClB,WAAW,CAAC,SAAS,EAAG,iBAAgBG,IAAI,CAACD,OAAQ,GAAE,EAAEC,IAAI,CAAC;MAChE,CAAC,MAAM;QACL,MAAM8B,SAAS,GAAG,MAAMzB,QAAQ,CAAC0B,IAAI,EAAE;QACvClC,WAAW,CAAC,OAAO,EAAG,mBAAkBQ,QAAQ,CAACW,MAAO,EAAC,EAAE,IAAI,EAAEc,SAAS,CAAC;MAC7E;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdP,WAAW,CAAC,OAAO,EAAE,wBAAwB,EAAE,IAAI,EAAEO,KAAK,CAACL,OAAO,CAAC;IACrE;IACAN,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMuC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,KAAK,MAAMjC,OAAO,IAAIH,YAAY,EAAE;MAClC,MAAMqB,UAAU,CAAClB,OAAO,CAAC;MACzB,MAAM,IAAIkC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3D;EACF,CAAC;;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B7C,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,MAAM8C,cAAc,GAAIvC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,OAAO;QAAE,OAAO,QAAQ;MAC7B,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B;QAAS,OAAO,WAAW;IAAC;EAEhC,CAAC;EAED,MAAMwC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,QAAQ5C,gBAAgB;MACtB,KAAK,WAAW;QACd,oBAAOT,OAAA,CAACH,KAAK;UAACyD,EAAE,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAQ;MAC9C,KAAK,OAAO;QACV,oBAAO3D,OAAA,CAACH,KAAK;UAACyD,EAAE,EAAC,QAAQ;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAQ;MAChD;QACE,oBAAO3D,OAAA,CAACH,KAAK;UAACyD,EAAE,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAQ;IAAC;EAEnD,CAAC;EAED,oBACE3D,OAAA,CAACP,IAAI;IAAA8D,QAAA,gBACHvD,OAAA,CAACP,IAAI,CAACmE,MAAM;MAAAL,QAAA,eACVvD,OAAA;QAAK6D,SAAS,EAAC,mDAAmD;QAAAN,QAAA,gBAChEvD,OAAA;UAAAuD,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK,EACzBN,kBAAkB,EAAE;MAAA;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACM,eACd3D,OAAA,CAACP,IAAI,CAACqE,IAAI;MAAAP,QAAA,gBAERvD,OAAA;QAAK6D,SAAS,EAAC,MAAM;QAAAN,QAAA,gBACnBvD,OAAA,CAACN,MAAM;UACLqE,OAAO,EAAC,iBAAiB;UACzBC,OAAO,EAAErC,cAAe;UACxBsC,QAAQ,EAAE1D,SAAU;UACpBsD,SAAS,EAAC,MAAM;UAAAN,QAAA,EACjB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS,eACT3D,OAAA,CAACN,MAAM;UACLqE,OAAO,EAAC,iBAAiB;UACzBC,OAAO,EAAEjB,eAAgB;UACzBkB,QAAQ,EAAE1D,SAAU;UACpBsD,SAAS,EAAC,MAAM;UAAAN,QAAA,EACjB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS,eACT3D,OAAA,CAACN,MAAM;UACLqE,OAAO,EAAC,mBAAmB;UAC3BC,OAAO,EAAEb,cAAe;UACxBc,QAAQ,EAAE1D,SAAU;UAAAgD,QAAA,EACrB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAS;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACL,eAGN3D,OAAA,CAACL,IAAI;QAACkE,SAAS,EAAC,MAAM;QAAAN,QAAA,eACpBvD,OAAA;UAAK6D,SAAS,EAAC,QAAQ;UAAAN,QAAA,gBACrBvD,OAAA,CAACL,IAAI,CAACuE,OAAO;YACXrD,IAAI,EAAC,MAAM;YACXsD,WAAW,EAAC,uBAAuB;YACnCC,KAAK,EAAEjE,WAAY;YACnBkE,QAAQ,EAAGC,CAAC,IAAKlE,cAAc,CAACkE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDI,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAIzC,UAAU,CAAC7B,WAAW;UAAE;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAChE,eACF3D,OAAA,CAACN,MAAM;YACLqE,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAEA,CAAA,KAAMhC,UAAU,CAAC7B,WAAW,CAAE;YACvC8D,QAAQ,EAAE1D,SAAS,IAAI,CAACJ,WAAW,CAACuE,IAAI,EAAG;YAC3Cb,SAAS,EAAC,MAAM;YAAAN,QAAA,EACjB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAS;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MACL;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD,eAGP3D,OAAA;QAAK6D,SAAS,EAAC,MAAM;QAAAN,QAAA,gBACnBvD,OAAA;UAAAuD,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK,eACrB3D,OAAA;UAAK6D,SAAS,EAAC,wBAAwB;UAAAN,QAAA,EACpC5C,YAAY,CAACgE,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAC3B7E,OAAA,CAACN,MAAM;YAELqE,OAAO,EAAC,cAAc;YACtBe,IAAI,EAAC,IAAI;YACTd,OAAO,EAAEA,CAAA,KAAMhC,UAAU,CAAC4C,GAAG,CAAE;YAC/BX,QAAQ,EAAE1D,SAAU;YAAAgD,QAAA,EAEnBqB;UAAG,GANCC,KAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAQb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACF,eAGN3D,OAAA;QAAK6D,SAAS,EAAC,MAAM;QAAAN,QAAA,gBACnBvD,OAAA;UAAAuD,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK,EAC9BzB,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,gBACjCnC,OAAA,CAACJ,KAAK;UAACmE,OAAO,EAAC,SAAS;UAACF,SAAS,EAAC,MAAM;UAAAN,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAQ,gBAER3D,OAAA,CAACJ,KAAK;UAACmE,OAAO,EAAC,SAAS;UAACF,SAAS,EAAC,MAAM;UAAAN,QAAA,GAAC,2CACT,eAAAvD,OAAA;YAAG+E,IAAI,EAAC,QAAQ;YAAAxB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAI,WAC3D;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACG,EAGLtD,SAAS,CAACY,MAAM,GAAG,CAAC,iBACnBjB,OAAA;QAAAuD,QAAA,gBACEvD,OAAA;UAAAuD,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK,eACtB3D,OAAA,CAACF,SAAS;UAACkF,KAAK,EAAE;YAAEC,SAAS,EAAE,OAAO;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAA3B,QAAA,EACzDlD,SAAS,CAACsE,GAAG,CAAEvD,QAAQ,iBACtBpB,OAAA,CAACF,SAAS,CAACqF,IAAI;YAEbpB,OAAO,EAAEX,cAAc,CAAChC,QAAQ,CAACP,IAAI,CAAE;YAAA0C,QAAA,eAEvCvD,OAAA;cAAK6D,SAAS,EAAC,kDAAkD;cAAAN,QAAA,gBAC/DvD,OAAA;gBAAK6D,SAAS,EAAC,aAAa;gBAAAN,QAAA,gBAC1BvD,OAAA;kBAAAuD,QAAA,EAASnC,QAAQ,CAACN;gBAAO;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAAU,EAClCvC,QAAQ,CAACL,IAAI,iBACZf,OAAA;kBAAS6D,SAAS,EAAC,MAAM;kBAAAN,QAAA,gBACvBvD,OAAA;oBAAAuD,QAAA,EAAS;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QAAU,eAChC3D,OAAA;oBAAK6D,SAAS,EAAC,MAAM;oBAACmB,KAAK,EAAE;sBAACI,QAAQ,EAAE,QAAQ;sBAAEH,SAAS,EAAE,OAAO;sBAAEI,QAAQ,EAAE;oBAAM,CAAE;oBAAA9B,QAAA,EACrFnB,IAAI,CAACQ,SAAS,CAACxB,QAAQ,CAACL,IAAI,EAAE,IAAI,EAAE,CAAC;kBAAC;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,QACnC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAET,EACAvC,QAAQ,CAACD,KAAK,iBACbnB,OAAA;kBAAK6D,SAAS,EAAC,kBAAkB;kBAACmB,KAAK,EAAE;oBAACI,QAAQ,EAAE;kBAAQ,CAAE;kBAAA7B,QAAA,GAAC,SACtD,EAACnC,QAAQ,CAACD,KAAK;gBAAA;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,QAEzB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QACG,eACN3D,OAAA;gBAAO6D,SAAS,EAAC,iBAAiB;gBAAAN,QAAA,EAAEnC,QAAQ,CAACI;cAAS;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,QAAS;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA;UAC3D,GArBDvC,QAAQ,CAACC,EAAE;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAuBnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACQ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAEf,eAGD3D,OAAA;QAAK6D,SAAS,EAAC,MAAM;QAAAN,QAAA,gBACnBvD,OAAA;UAAAuD,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAAK,eACtB3D,OAAA;UAAI6D,SAAS,EAAC,OAAO;UAAAN,QAAA,gBACnBvD,OAAA;YAAAuD,QAAA,EAAI;UAA+C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eACxD3D,OAAA;YAAAuD,QAAA,EAAI;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eAC1C3D,OAAA;YAAAuD,QAAA,EAAI;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eAClD3D,OAAA;YAAAuD,QAAA,EAAI;UAA+C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK,eACxD3D,OAAA;YAAAuD,QAAA,EAAI;UAAqC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,QAAK;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QAC3C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QACD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACI;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACP;AAEX,CAAC;AAACzD,EAAA,CArQID,YAAY;AAAAqF,EAAA,GAAZrF,YAAY;AAuQlB,eAAeA,YAAY;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}